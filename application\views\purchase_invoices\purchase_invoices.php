<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-list text-blue"></i> Data Purchase Invoices</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_purchase_invoices" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Invoice No</th>
                                    <th>Invoice Date</th>
                                    <th>Supplier</th>
                                    <th>PO No</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<!-- Modal Form -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Purchase Invoice Form</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body form">
                <form action="#" id="form" class="form-horizontal">
                    <input type="hidden" value="" name="id"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Invoice Number</label>
                                    <div class="input-group">
                                        <input name="invoice_no" placeholder="Invoice Number" class="form-control" type="text" readonly>
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" onclick="generateInvoiceNumber()">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Supplier</label>
                                    <select name="supplier_id" class="form-control select2" style="width: 100%;">
                                        <option value="">Pilih Supplier</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Purchase Order</label>
                                    <select name="po_id" class="form-control select2" style="width: 100%;">
                                        <option value="">Pilih Purchase Order</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Invoice Date</label>
                                    <input name="invoice_date" placeholder="Invoice Date" class="form-control" type="date">
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Due Date</label>
                                    <input name="due_date" placeholder="Due Date" class="form-control" type="date">
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Reference No</label>
                                    <input name="reference_no" placeholder="Reference Number" class="form-control" type="text">
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Status</label>
                                    <select name="status" class="form-control">
                                        <option value="unpaid">Unpaid</option>
                                        <option value="partial">Partial</option>
                                        <option value="paid">Paid</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Discount Type</label>
                                    <select name="discount_type" class="form-control">
                                        <option value="percentage">Percentage</option>
                                        <option value="fixed">Fixed Amount</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Discount Value</label>
                                    <input name="discount_value" placeholder="0" class="form-control" type="number" step="0.01" min="0">
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Tax Percentage (%)</label>
                                    <input name="tax_percentage" placeholder="0" class="form-control" type="number" step="0.01" min="0" max="100">
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Shipping Cost</label>
                                    <input name="shipping_cost" placeholder="0" class="form-control" type="number" step="0.01" min="0">
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Total Amount</label>
                                    <input name="total_amount" placeholder="0" class="form-control" type="number" step="0.01" min="0">
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Paid Amount</label>
                                    <input name="paid_amount" placeholder="0" class="form-control" type="number" step="0.01" min="0">
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="control-label">Notes</label>
                                    <textarea name="notes" placeholder="Notes" class="form-control" rows="3"></textarea>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- End Bootstrap modal -->

<script type="text/javascript">
var table;
$(document).ready(function() {
    //datatables
    table = $("#tbl_purchase_invoices").DataTable({
        "responsive": true,
        "autoWidth": false,
        "language": {
            "sEmptyTable": "Belum Ada Data Purchase Invoices"
        },
        "processing": true,
        "serverSide": true,
        "order": [],

        "ajax": {
            "url": "<?= base_url('purchase_invoices/ajax_list') ?>",
            "type": "POST"
        },

        "columnDefs": [{
            "targets": [-1],
            "orderable": false,
        }, ],

    });

    // Load suppliers for dropdown
    loadSuppliers();
    
    // Load purchase orders for dropdown
    loadPurchaseOrders();

    // Set default date to today
    $('input[name="invoice_date"]').val(new Date().toISOString().split('T')[0]);
    
    // Set due date to 30 days from today
    var dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);
    $('input[name="due_date"]').val(dueDate.toISOString().split('T')[0]);
});

function loadSuppliers() {
    $.ajax({
        url: "<?= base_url('purchase_invoices/get_suppliers') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            var html = '<option value="">Pilih Supplier</option>';
            for (var i = 0; i < data.length; i++) {
                html += '<option value="' + data[i].id + '">' + data[i].nama + '</option>';
            }
            $('select[name="supplier_id"]').html(html);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error loading suppliers');
        }
    });
}

function loadPurchaseOrders() {
    $.ajax({
        url: "<?= base_url('purchase_invoices/get_purchase_orders') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            var html = '<option value="">Pilih Purchase Order</option>';
            for (var i = 0; i < data.length; i++) {
                html += '<option value="' + data[i].id + '" data-supplier="' + data[i].supplier_id + '">' + data[i].po_no + ' - ' + data[i].order_date + ' (Rp ' + number_format(data[i].total_amount) + ')</option>';
            }
            $('select[name="po_id"]').html(html);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error loading purchase orders');
        }
    });
}

// Auto select supplier when PO is selected
$('select[name="po_id"]').change(function() {
    var supplier_id = $(this).find(':selected').data('supplier');
    if (supplier_id) {
        $('select[name="supplier_id"]').val(supplier_id).trigger('change');
    }
});

function generateInvoiceNumber() {
    $.ajax({
        url: "<?= base_url('purchase_invoices/generate_invoice_number') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('input[name="invoice_no"]').val(data.invoice_no);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error generating invoice number');
        }
    });
}

function add() {
    save_method = 'add';
    $('#form')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('#modal_form').modal('show');
    $('.modal-title').text('Add Purchase Invoice');
    generateInvoiceNumber();
}

function edit(id) {
    save_method = 'update';
    $('#form')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    $.ajax({
        url: "<?= base_url('purchase_invoices/edit/') ?>" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('[name="id"]').val(data.id);
            $('[name="invoice_no"]').val(data.invoice_no);
            $('[name="supplier_id"]').val(data.supplier_id).trigger('change');
            $('[name="po_id"]').val(data.po_id).trigger('change');
            $('[name="invoice_date"]').val(data.invoice_date);
            $('[name="due_date"]').val(data.due_date);
            $('[name="reference_no"]').val(data.reference_no);
            $('[name="notes"]').val(data.notes);
            $('[name="discount_type"]').val(data.discount_type);
            $('[name="discount_value"]').val(data.discount_value);
            $('[name="tax_percentage"]').val(data.tax_percentage);
            $('[name="shipping_cost"]').val(data.shipping_cost);
            $('[name="total_amount"]').val(data.total_amount);
            $('[name="paid_amount"]').val(data.paid_amount);
            $('[name="status"]').val(data.status);

            $('#modal_form').modal('show');
            $('.modal-title').text('Edit Purchase Invoice');

        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error get data from ajax');
        }
    });
}

function reload_table() {
    table.ajax.reload(null, false);
}

function save() {
    $('#btnSave').text('saving...');
    $('#btnSave').attr('disabled', true);
    var url;

    if (save_method == 'add') {
        url = "<?= base_url('purchase_invoices/insert') ?>";
    } else {
        url = "<?= base_url('purchase_invoices/update') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status) {
                $('#modal_form').modal('hide');
                reload_table();
                toastr.success('Data berhasil disimpan');
            }
            $('#btnSave').text('save');
            $('#btnSave').attr('disabled', false);

        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error adding / update data');
            $('#btnSave').text('save');
            $('#btnSave').attr('disabled', false);

        }
    });
}

function hapus(id) {
    if (confirm('Are you sure delete this data?')) {
        $.ajax({
            url: "<?= base_url('purchase_invoices/delete') ?>",
            type: "POST",
            dataType: "JSON",
            data: {
                id: id
            },
            success: function(data) {
                $('#modal_form').modal('hide');
                reload_table();
                toastr.success('Data berhasil dihapus');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error deleting data');
            }
        });

    }
}

function number_format(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}
</script>
