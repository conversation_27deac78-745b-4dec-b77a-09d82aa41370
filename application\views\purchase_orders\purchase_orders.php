<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-list text-blue"></i> Data Purchase Orders</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_purchase_orders" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>PO No</th>
                                    <th>Order Date</th>
                                    <th>Supplier</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Total</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<!-- Modal Form -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Purchase Order Form</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body form">
                <form action="#" id="form" class="form-horizontal">
                    <input type="hidden" value="" name="id"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">PO Number</label>
                                    <div class="input-group">
                                        <input name="po_no" placeholder="PO Number" class="form-control" type="text" readonly>
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" onclick="generatePONumber()">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Supplier</label>
                                    <select name="supplier_id" class="form-control" required>
                                        <option value="">Pilih Supplier</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Order Date</label>
                                    <input name="order_date" placeholder="Order Date" class="form-control" type="date" required>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Due Date</label>
                                    <input name="due_date" placeholder="Due Date" class="form-control" type="date">
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Status</label>
                                    <select name="status" class="form-control">
                                        <option value="draft">Draft</option>
                                        <option value="ordered">Ordered</option>
                                        <option value="received">Received</option>
                                        <option value="invoiced">Invoiced</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Priority</label>
                                    <select name="priority" class="form-control">
                                        <option value="low">Low</option>
                                        <option value="normal">Normal</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Notes</label>
                            <textarea name="notes" placeholder="Notes" class="form-control" rows="3"></textarea>
                            <span class="help-block"></span>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="control-label">Discount Type</label>
                                    <select name="discount_type" class="form-control">
                                        <option value="percentage">Percentage</option>
                                        <option value="fixed">Fixed Amount</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="control-label">Discount Value</label>
                                    <input name="discount_value" placeholder="0" class="form-control" type="number" step="0.01" min="0">
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="control-label">Tax (%)</label>
                                    <input name="tax_percentage" placeholder="0" class="form-control" type="number" step="0.01" min="0" max="100">
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Shipping Cost</label>
                                    <input name="shipping_cost" placeholder="0" class="form-control" type="number" step="0.01" min="0">
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Total Amount</label>
                                    <input name="total_amount" placeholder="0" class="form-control" type="number" step="0.01" min="0" readonly>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script type="text/javascript">
var table;
$(document).ready(function() {
    //datatables
    table = $("#tbl_purchase_orders").DataTable({
        "responsive": true,
        "autoWidth": false,
        "language": {
            "sEmptyTable": "Belum Ada Data Purchase Orders"
        },
        "processing": true,
        "serverSide": true,
        "order": [],

        "ajax": {
            "url": "<?= base_url('purchase_orders/ajax_list') ?>",
            "type": "POST"
        },

        "columnDefs": [{
            "targets": [-1],
            "orderable": false,
        }, ],

    });

    // Load suppliers for dropdown
    loadSuppliers();

    // Set default date to today
    $('input[name="order_date"]').val(new Date().toISOString().split('T')[0]);
});

function loadSuppliers() {
    $.ajax({
        url: "<?= base_url('purchase_orders/get_suppliers') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            var options = '<option value="">Pilih Supplier</option>';
            $.each(data, function(index, supplier) {
                options += '<option value="' + supplier.id + '">' + supplier.nama + '</option>';
            });
            $('select[name="supplier_id"]').html(options);
        }
    });
}

function generatePONumber() {
    $.ajax({
        url: "<?= base_url('purchase_orders/generate_po_number') ?>",
        type: "POST",
        dataType: "JSON",
        success: function(data) {
            $('input[name="po_no"]').val(data.po_no);
        }
    });
}

function add() {
    save_method = 'add';
    $('#form')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('#modal_form').modal('show');
    $('.modal-title').text('Add Purchase Order');
    generatePONumber();
}

function edit(id) {
    save_method = 'update';
    $('#form')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    $.ajax({
        url: "<?= base_url('purchase_orders/edit/') ?>" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('[name="id"]').val(data.id);
            $('[name="po_no"]').val(data.po_no);
            $('[name="supplier_id"]').val(data.supplier_id);
            $('[name="order_date"]').val(data.order_date);
            $('[name="due_date"]').val(data.due_date);
            $('[name="status"]').val(data.status);
            $('[name="priority"]').val(data.priority);
            $('[name="notes"]').val(data.notes);
            $('[name="discount_type"]').val(data.discount_type);
            $('[name="discount_value"]').val(data.discount_value);
            $('[name="tax_percentage"]').val(data.tax_percentage);
            $('[name="shipping_cost"]').val(data.shipping_cost);
            $('[name="total_amount"]').val(data.total_amount);

            $('#modal_form').modal('show');
            $('.modal-title').text('Edit Purchase Order');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error get data from ajax');
        }
    });
}

function reload_table() {
    table.ajax.reload(null, false);
}

function save() {
    $('#btnSave').text('saving...');
    $('#btnSave').attr('disabled', true);
    var url;

    if (save_method == 'add') {
        url = "<?= base_url('purchase_orders/insert') ?>";
    } else {
        url = "<?= base_url('purchase_orders/update') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status) {
                $('#modal_form').modal('hide');
                reload_table();
                toastr.success('Data berhasil disimpan');
            }
            $('#btnSave').text('save');
            $('#btnSave').attr('disabled', false);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error adding / update data');
            $('#btnSave').text('save');
            $('#btnSave').attr('disabled', false);
        }
    });
}

function hapus(id) {
    if (confirm('Are you sure delete this data?')) {
        $.ajax({
            url: "<?= base_url('purchase_orders/delete') ?>",
            type: "POST",
            dataType: "JSON",
            data: {
                id: id
            },
            success: function(data) {
                if (data.status) {
                    reload_table();
                    toastr.success('Data berhasil dihapus');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error deleting data');
            }
        });
    }
}

function detail(id) {
    window.location.href = "<?= base_url('purchase_orders/detail/') ?>" + id;
}
</script>
