<style>
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

.modern-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.btn-modern {
    border-radius: 8px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.info-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: none;
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.info-card-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--gray-200);
}

.info-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
}

.info-card-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-draft { background-color: #f8f9fa; color: #6c757d; }
.status-ordered { background-color: #fff3cd; color: #856404; }
.status-received { background-color: #d1ecf1; color: #0c5460; }
.status-invoiced { background-color: #d4edda; color: #155724; }

.priority-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: capitalize;
}

.priority-low { background-color: #f8f9fa; color: #6c757d; }
.priority-normal { background-color: #cce5ff; color: #004085; }
.priority-high { background-color: #fff3cd; color: #856404; }
.priority-urgent { background-color: #f8d7da; color: #721c24; }

.order-items-bar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: none;
    transition: all 0.3s ease;
}

.order-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.order-items-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
}

.order-items-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.btn-add-item {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.btn-add-item:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-add-item i {
    margin-right: 0.5rem;
}

.order-items-table {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.order-items-table thead th {
    background: var(--gray-100);
    border: none;
    font-weight: 600;
    color: var(--gray-700);
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
}

.order-items-table tbody td {
    border: none;
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
}

.order-items-table tbody tr:hover {
    background-color: var(--gray-50);
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: none;
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.summary-card-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
}

.summary-card-icon.items {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.summary-card-icon.quantity {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.summary-card-icon.total {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.summary-card-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.summary-card-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.financial-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.financial-summary h6 {
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
}

.financial-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.financial-row:last-child {
    border-bottom: none;
    margin-top: 0.5rem;
    padding-top: 1rem;
    border-top: 2px solid rgba(255, 255, 255, 0.3);
}

.financial-label {
    font-weight: 500;
}

.financial-value {
    font-weight: 600;
}

.final-total-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 2px;
}

.final-total-note {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.toggle-icon.rotated {
    transform: rotate(180deg);
}
</style>

<!-- Main content -->
<section class="content modern-container">
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1 font-weight-bold text-white">Purchase Order <?= $purchase_order->po_no ?></h2>
                <p class="text-white-50 mb-0"><?= date('d M Y, H:i', strtotime($purchase_order->created_at)) ?></p>
            </div>
            <a href="<?= base_url('purchase_orders') ?>" class="btn btn-outline-light btn-modern">
                <i class="fas fa-arrow-left mr-2"></i>Back
            </a>
        </div>

        <!-- Purchase Order Information -->
        <div class="info-card">
            <div class="info-card-header">
                <h6 class="info-card-title">
                    <i class="fas fa-info-circle"></i>
                    Purchase Order Information
                </h6>
                <div>
                    <span class="status-badge status-<?= $purchase_order->status ?>">
                        <?= ucfirst($purchase_order->status) ?>
                    </span>
                    <span class="priority-badge priority-<?= $purchase_order->priority ?> ml-2">
                        <?= ucfirst($purchase_order->priority) ?> Priority
                    </span>
                </div>
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">PO Number</span>
                    <span class="info-value"><?= $purchase_order->po_no ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Order Date</span>
                    <span class="info-value"><?= date('d M Y', strtotime($purchase_order->order_date)) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Due Date</span>
                    <span class="info-value"><?= $purchase_order->due_date ? date('d M Y', strtotime($purchase_order->due_date)) : '-' ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Total Amount</span>
                    <span class="info-value">Rp <?= number_format($purchase_order->total_amount, 0, ',', '.') ?></span>
                </div>
            </div>
        </div>

        <!-- Supplier Information -->
        <div class="info-card">
            <div class="info-card-header">
                <h6 class="info-card-title">
                    <i class="fas fa-truck"></i>
                    Supplier Information
                </h6>
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Supplier Name</span>
                    <span class="info-value"><?= $purchase_order->supplier_name ? $purchase_order->supplier_name : '-' ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Phone</span>
                    <span class="info-value"><?= $purchase_order->supplier_phone ? $purchase_order->supplier_phone : '-' ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Address</span>
                    <span class="info-value"><?= $purchase_order->supplier_address ? $purchase_order->supplier_address : '-' ?></span>
                </div>
            </div>
        </div>

        <!-- Additional Information (Collapsible) -->
        <div class="info-card">
            <div class="info-card-header">
                <h6 class="info-card-title">
                    <i class="fas fa-plus-circle"></i>
                    Additional Information
                </h6>
                <button class="btn btn-link p-0" type="button" data-toggle="collapse" data-target="#additionalInfo" aria-expanded="false">
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </button>
            </div>
            <div class="collapse" id="additionalInfo">
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Discount Type</span>
                        <span class="info-value"><?= ucfirst($purchase_order->discount_type) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Discount Value</span>
                        <span class="info-value">
                            <?php if($purchase_order->discount_type == 'percentage'): ?>
                                <?= $purchase_order->discount_value ?>%
                            <?php else: ?>
                                Rp <?= number_format($purchase_order->discount_value, 0, ',', '.') ?>
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Tax Percentage</span>
                        <span class="info-value"><?= $purchase_order->tax_percentage ?>%</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Shipping Cost</span>
                        <span class="info-value">Rp <?= number_format($purchase_order->shipping_cost, 0, ',', '.') ?></span>
                    </div>
                </div>
                <?php if($purchase_order->notes): ?>
                <div class="mt-3">
                    <span class="info-label">Notes</span>
                    <p class="info-value mt-1"><?= nl2br(htmlspecialchars($purchase_order->notes)) ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Purchase Order Items -->
        <div class="order-items-bar" style="padding: 15px; margin-bottom: 20px;">
            <div class="order-items-header">
                <h6 class="order-items-title">
                    <i class="fas fa-shopping-cart"></i>
                    Purchase Order Items
                </h6>
                <button type="button" class="btn-add-item" onclick="addDetail()">
                    <i class="fas fa-plus"></i>Add Item
                </button>
            </div>

            <div class="table-responsive">
                <table id="tbl_purchase_order_detail" class="order-items-table table mb-0">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Unit</th>
                            <th class="text-center">Qty</th>
                            <th class="text-right">Price</th>
                            <th class="text-right">Subtotal</th>
                            <th>Notes</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card">
                <div class="summary-card-icon items">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="summary-card-value" id="total-items">0</div>
                <div class="summary-card-label">Total Items</div>
            </div>
            <div class="summary-card">
                <div class="summary-card-icon quantity">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="summary-card-value" id="total-qty">0</div>
                <div class="summary-card-label">Total Quantity</div>
            </div>
            <div class="summary-card">
                <div class="summary-card-icon total">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="summary-card-value" id="grand-total">Rp 0</div>
                <div class="summary-card-label">Subtotal</div>
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="financial-summary">
            <h6><i class="fas fa-calculator mr-2"></i>Financial Summary</h6>
            <div class="financial-row">
                <span class="financial-label">Subtotal:</span>
                <span class="financial-value" id="subtotal-amount">Rp 0</span>
            </div>
            <div class="financial-row">
                <span class="financial-label">Discount:</span>
                <span class="financial-value" id="discount-amount">Rp 0</span>
            </div>
            <div class="financial-row">
                <span class="financial-label">After Discount:</span>
                <span class="financial-value" id="after-discount-amount">Rp 0</span>
            </div>
            <div class="financial-row">
                <span class="financial-label">Tax:</span>
                <span class="financial-value" id="tax-amount">Rp 0</span>
            </div>
            <div class="financial-row">
                <span class="financial-label">Shipping:</span>
                <span class="financial-value" id="shipping-amount">Rp <?= number_format($purchase_order->shipping_cost, 0, ',', '.') ?></span>
            </div>
            <div class="financial-row">
                <span class="financial-label">Total Amount:</span>
                <div>
                    <div class="final-total-value">Rp 0</div>
                    <div class="final-total-note">Including all taxes and fees</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form for Purchase Order Items -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Purchase Order Item Form</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body form">
                <form action="#" id="form_detail" class="form-horizontal">
                    <input type="hidden" value="" name="id"/>
                    <input type="hidden" value="<?= $purchase_order_id ?>" name="purchase_order_id"/>
                    <div class="form-body">
                        <div class="form-group">
                            <label class="control-label">Item</label>
                            <select name="id_barang" class="form-control" required>
                                <option value="">Pilih Barang</option>
                            </select>
                            <span class="help-block"></span>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Quantity</label>
                                    <input name="quantity" placeholder="Quantity" class="form-control" type="number" step="0.01" min="0" required>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Unit Price</label>
                                    <input name="unit_price" placeholder="Unit Price" class="form-control" type="number" step="0.01" min="0" required>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Discount</label>
                            <input name="discount" placeholder="Discount" class="form-control" type="number" step="0.01" min="0">
                            <span class="help-block"></span>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Notes</label>
                            <textarea name="notes" placeholder="Notes" class="form-control" rows="3"></textarea>
                            <span class="help-block"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetail" onclick="saveDetail()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
var table_detail;
var save_method_detail;
var purchase_order_id = <?= $purchase_order_id ?>;

function number_format(number, decimals, dec_point, thousands_sep) {
    number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
    var n = !isFinite(+number) ? 0 : +number,
        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
        sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
        dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
        s = '',
        toFixedFix = function(n, prec) {
            var k = Math.pow(10, prec);
            return '' + Math.round(n * k) / k;
        };
    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || '').length < prec) {
        s[1] = s[1] || '';
        s[1] += new Array(prec - s[1].length + 1).join('0');
    }
    return s.join(dec);
}

$(document).ready(function() {
    //datatables for purchase order detail
    table_detail = $("#tbl_purchase_order_detail").DataTable({
        "responsive": true,
        "autoWidth": false,
        "language": {
            "sEmptyTable": "Belum Ada Item Purchase Order"
        },
        "processing": true,
        "serverSide": true,
        "order": [],

        "ajax": {
            "url": "<?= base_url('purchase_orders/ajax_list_detail') ?>",
            "type": "POST",
            "data": function(d) {
                d.purchase_order_id = purchase_order_id;
            }
        },
        "drawCallback": function(settings) {
            updatePurchaseOrderSummary();
            calculateTotal();
        }
    });

    // Load barang for dropdown
    loadBarang();

    // Additional Information toggle animation
    $('#additionalInfo').on('show.bs.collapse', function () {
        $('.toggle-icon').addClass('rotated');
    });

    $('#additionalInfo').on('hide.bs.collapse', function () {
        $('.toggle-icon').removeClass('rotated');
    });

    // Initial load
    updatePurchaseOrderSummary();
    calculateTotal();
});

function loadBarang() {
    $.ajax({
        url: "<?= base_url('purchase_orders/get_barang') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            var options = '<option value="">Pilih Barang</option>';
            $.each(data, function(index, barang) {
                options += '<option value="' + barang.id + '">' + barang.nama + '</option>';
            });
            $('select[name="id_barang"]').html(options);
        }
    });
}

function updatePurchaseOrderSummary() {
    $.ajax({
        url: "<?= base_url('purchase_orders/get_purchase_order_summary') ?>",
        type: "POST",
        data: {purchase_order_id: purchase_order_id},
        dataType: "JSON",
        success: function(data) {
            $('#total-items').text(data.total_items);
            $('#total-qty').text(parseFloat(data.total_qty).toFixed(2));
            $('#grand-total').text('Rp ' + number_format(data.grand_total, 0, ',', '.'));
        }
    });
}

function calculateTotal() {
    $.ajax({
        url: "<?= base_url('purchase_orders/calculate_total') ?>",
        type: "POST",
        data: {purchase_order_id: purchase_order_id},
        dataType: "JSON",
        success: function(data) {
            // Update financial summary
            $('#subtotal-amount').text('Rp ' + number_format(data.subtotal, 0, ',', '.'));
            $('#discount-amount').text('Rp ' + number_format(data.discount_amount, 0, ',', '.'));
            $('#after-discount-amount').text('Rp ' + number_format(data.after_discount, 0, ',', '.'));
            $('#tax-amount').text('Rp ' + number_format(data.tax_amount, 0, ',', '.'));
            $('.final-total-value').text('Rp ' + number_format(data.total_amount, 0, ',', '.'));
        }
    });
}

function addDetail() {
    save_method_detail = 'add';
    $('#form_detail')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('#modal_form_detail').modal('show');
    $('.modal-title').text('Add Purchase Order Item');
}

function editDetail(id) {
    save_method_detail = 'update';
    $('#form_detail')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    $.ajax({
        url: "<?= base_url('purchase_orders/edit_detail/') ?>" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('[name="id"]').val(data.id);
            $('[name="id_barang"]').val(data.id_barang);
            $('[name="quantity"]').val(data.quantity);
            $('[name="unit_price"]').val(data.unit_price);
            $('[name="discount"]').val(data.discount);
            $('[name="notes"]').val(data.notes);

            $('#modal_form_detail').modal('show');
            $('.modal-title').text('Edit Purchase Order Item');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error get data from ajax');
        }
    });
}

function saveDetail() {
    $('#btnSaveDetail').text('saving...');
    $('#btnSaveDetail').attr('disabled', true);
    var url;

    if (save_method_detail == 'add') {
        url = "<?= base_url('purchase_orders/insert_detail') ?>";
    } else {
        url = "<?= base_url('purchase_orders/update_detail') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form_detail').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status) {
                $('#modal_form_detail').modal('hide');
                table_detail.ajax.reload(null, false);
                toastr.success('Data berhasil disimpan');
            }
            $('#btnSaveDetail').text('save');
            $('#btnSaveDetail').attr('disabled', false);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error adding / update data');
            $('#btnSaveDetail').text('save');
            $('#btnSaveDetail').attr('disabled', false);
        }
    });
}

function hapusDetail(id) {
    if (confirm('Are you sure delete this item?')) {
        $.ajax({
            url: "<?= base_url('purchase_orders/delete_detail') ?>",
            type: "POST",
            dataType: "JSON",
            data: {
                id: id
            },
            success: function(data) {
                if (data.status) {
                    table_detail.ajax.reload(null, false);
                    toastr.success('Data berhasil dihapus');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error deleting data');
            }
        });
    }
}
</script>