<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Purchase Invoice Header -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title">
                            <i class="fas fa-file-invoice text-blue"></i>
                            Purchase Invoice Detail - <?= $purchase_invoice->invoice_no ?>
                        </h3>
                        <div class="card-tools">
                            <a href="<?= base_url('purchase_invoices') ?>" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5><strong>Invoice Information</strong></h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="150"><strong>Invoice No:</strong></td>
                                        <td><?= $purchase_invoice->invoice_no ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Invoice Date:</strong></td>
                                        <td><?= date('d-m-Y', strtotime($purchase_invoice->invoice_date)) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Due Date:</strong></td>
                                        <td><?= $purchase_invoice->due_date ? date('d-m-Y', strtotime($purchase_invoice->due_date)) : '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Reference No:</strong></td>
                                        <td><?= $purchase_invoice->reference_no ? $purchase_invoice->reference_no : '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>PO Number:</strong></td>
                                        <td><?= $purchase_invoice->po_no ? $purchase_invoice->po_no : '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            switch ($purchase_invoice->status) {
                                                case 'paid':
                                                    $status_class = 'badge-success';
                                                    break;
                                                case 'partial':
                                                    $status_class = 'badge-warning';
                                                    break;
                                                case 'unpaid':
                                                    $status_class = 'badge-danger';
                                                    break;
                                                default:
                                                    $status_class = 'badge-secondary';
                                            }
                                            ?>
                                            <span class="badge <?= $status_class ?>"><?= ucfirst($purchase_invoice->status) ?></span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5><strong>Supplier Information</strong></h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="150"><strong>Supplier:</strong></td>
                                        <td><?= $purchase_invoice->supplier_name ? $purchase_invoice->supplier_name : '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td><?= $purchase_invoice->supplier_address ? $purchase_invoice->supplier_address : '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td><?= $purchase_invoice->supplier_phone ? $purchase_invoice->supplier_phone : '-' ?></td>
                                    </tr>
                                </table>

                                <h5><strong>Financial Summary</strong></h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="150"><strong>Total Amount:</strong></td>
                                        <td><strong>Rp <?= number_format($purchase_invoice->total_amount, 0, ',', '.') ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Paid Amount:</strong></td>
                                        <td>Rp <?= number_format($purchase_invoice->paid_amount, 0, ',', '.') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Remaining:</strong></td>
                                        <td>Rp <?= number_format($purchase_invoice->remaining_amount, 0, ',', '.') ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Additional Information (Collapsible) -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-outline-info btn-sm" type="button" data-toggle="collapse" data-target="#additionalInfo" aria-expanded="false" aria-controls="additionalInfo">
                                    <i class="fas fa-info-circle toggle-icon"></i> Additional Information
                                </button>
                                <div class="collapse mt-2" id="additionalInfo">
                                    <div class="card card-body bg-light">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <table class="table table-sm table-borderless">
                                                    <tr>
                                                        <td width="150"><strong>Discount Type:</strong></td>
                                                        <td><?= ucfirst($purchase_invoice->discount_type) ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Discount Value:</strong></td>
                                                        <td>
                                                            <?php if ($purchase_invoice->discount_type == 'percentage'): ?>
                                                                <?= $purchase_invoice->discount_value ?>%
                                                            <?php else: ?>
                                                                Rp <?= number_format($purchase_invoice->discount_value, 0, ',', '.') ?>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Tax Percentage:</strong></td>
                                                        <td><?= $purchase_invoice->tax_percentage ?>%</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Shipping Cost:</strong></td>
                                                        <td>Rp <?= number_format($purchase_invoice->shipping_cost, 0, ',', '.') ?></td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <div class="col-md-6">
                                                <table class="table table-sm table-borderless">
                                                    <tr>
                                                        <td width="150"><strong>Created:</strong></td>
                                                        <td><?= date('d-m-Y H:i', strtotime($purchase_invoice->created_at)) ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Updated:</strong></td>
                                                        <td><?= date('d-m-Y H:i', strtotime($purchase_invoice->updated_at)) ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Notes:</strong></td>
                                                        <td><?= $purchase_invoice->notes ? $purchase_invoice->notes : '-' ?></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase Invoice Items -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fas fa-list text-blue"></i> Invoice Items</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add-detail" onclick="add_detail()" title="Add Item">
                                <i class="fas fa-plus"></i> Add Item
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <table id="tbl_purchase_invoice_detail" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Item</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Discount</th>
                                    <th>Subtotal</th>
                                    <th>Notes</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Summary Card -->
        <div class="row">
            <div class="col-md-6 offset-md-6">
                <div class="card">
                    <div class="card-header bg-info">
                        <h3 class="card-title text-white"><i class="fas fa-calculator"></i> Financial Summary</h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Items Subtotal:</strong></td>
                                <td class="text-right" id="items_subtotal">Rp 0</td>
                            </tr>
                            <tr>
                                <td><strong>Discount:</strong></td>
                                <td class="text-right" id="discount_amount">Rp 0</td>
                            </tr>
                            <tr>
                                <td><strong>Tax:</strong></td>
                                <td class="text-right" id="tax_amount">Rp 0</td>
                            </tr>
                            <tr>
                                <td><strong>Shipping:</strong></td>
                                <td class="text-right" id="shipping_amount">Rp <?= number_format($purchase_invoice->shipping_cost, 0, ',', '.') ?></td>
                            </tr>
                            <tr class="border-top">
                                <td><strong>Total Amount:</strong></td>
                                <td class="text-right"><strong id="total_amount">Rp <?= number_format($purchase_invoice->total_amount, 0, ',', '.') ?></strong></td>
                            </tr>
                            <tr>
                                <td><strong>Paid Amount:</strong></td>
                                <td class="text-right" id="paid_amount">Rp <?= number_format($purchase_invoice->paid_amount, 0, ',', '.') ?></td>
                            </tr>
                            <tr class="border-top">
                                <td><strong>Remaining Amount:</strong></td>
                                <td class="text-right"><strong id="remaining_amount">Rp <?= number_format($purchase_invoice->remaining_amount, 0, ',', '.') ?></strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form for Invoice Items -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Invoice Item Form</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body form">
                <form action="#" id="form_detail" class="form-horizontal">
                    <input type="hidden" value="" name="id"/>
                    <input type="hidden" value="<?= $purchase_invoice_id ?>" name="purchase_invoice_id"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Item</label>
                                    <select name="id_barang" class="form-control select2" style="width: 100%;">
                                        <option value="">Pilih Barang</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Quantity</label>
                                    <input name="quantity" placeholder="Quantity" class="form-control" type="number" step="0.01" min="0" onchange="calculateSubtotal()">
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Unit Price</label>
                                    <input name="unit_price" placeholder="Unit Price" class="form-control" type="number" step="0.01" min="0" onchange="calculateSubtotal()">
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Discount</label>
                                    <input name="discount" placeholder="Discount" class="form-control" type="number" step="0.01" min="0" onchange="calculateSubtotal()">
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Subtotal</label>
                                    <input name="subtotal" placeholder="Subtotal" class="form-control" type="number" step="0.01" readonly>
                                    <span class="help-block"></span>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Notes</label>
                                    <textarea name="notes" placeholder="Notes" class="form-control" rows="3"></textarea>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetail" onclick="save_detail()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
var table_detail;
var save_method_detail;
var purchase_invoice_id = <?= $purchase_invoice_id ?>;

$(document).ready(function() {
    //datatables for purchase invoice detail
    table_detail = $("#tbl_purchase_invoice_detail").DataTable({
        "responsive": true,
        "autoWidth": false,
        "language": {
            "sEmptyTable": "Belum Ada Item Purchase Invoice"
        },
        "processing": true,
        "serverSide": true,
        "order": [],

        "ajax": {
            "url": "<?= base_url('purchase_invoices/ajax_list_detail') ?>",
            "type": "POST",
            "data": function(d) {
                d.purchase_invoice_id = purchase_invoice_id;
            }
        },
        "drawCallback": function(settings) {
            updatePurchaseInvoiceSummary();
            calculateTotal();
        }
    });

    // Load barang for dropdown
    loadBarang();

    // Additional Information toggle animation
    $('#additionalInfo').on('show.bs.collapse', function () {
        $('.toggle-icon').addClass('rotated');
    });

    $('#additionalInfo').on('hide.bs.collapse', function () {
        $('.toggle-icon').removeClass('rotated');
    });
});

function loadBarang() {
    $.ajax({
        url: "<?= base_url('purchase_invoices/get_barang') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            var html = '<option value="">Pilih Barang</option>';
            for (var i = 0; i < data.length; i++) {
                html += '<option value="' + data[i].id_barang + '" data-price="' + data[i].harga_jual + '">' + data[i].nama + ' - Rp ' + number_format(data[i].harga_jual) + '</option>';
            }
            $('select[name="id_barang"]').html(html);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error loading barang');
        }
    });
}

// Auto fill unit price when barang is selected
$('select[name="id_barang"]').change(function() {
    var price = $(this).find(':selected').data('price');
    if (price) {
        $('input[name="unit_price"]').val(price);
        calculateSubtotal();
    }
});

function calculateSubtotal() {
    var quantity = parseFloat($('input[name="quantity"]').val()) || 0;
    var unit_price = parseFloat($('input[name="unit_price"]').val()) || 0;
    var discount = parseFloat($('input[name="discount"]').val()) || 0;

    var subtotal = (quantity * unit_price) - discount;
    $('input[name="subtotal"]').val(subtotal.toFixed(2));
}

function add_detail() {
    save_method_detail = 'add';
    $('#form_detail')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('#modal_form_detail').modal('show');
    $('.modal-title').text('Add Invoice Item');
}

function edit_detail(id) {
    save_method_detail = 'update';
    $('#form_detail')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    $.ajax({
        url: "<?= base_url('purchase_invoices/edit_detail/') ?>" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('[name="id"]').val(data.id);
            $('[name="id_barang"]').val(data.id_barang).trigger('change');
            $('[name="quantity"]').val(data.quantity);
            $('[name="unit_price"]').val(data.unit_price);
            $('[name="discount"]').val(data.discount);
            $('[name="notes"]').val(data.notes);
            calculateSubtotal();

            $('#modal_form_detail').modal('show');
            $('.modal-title').text('Edit Invoice Item');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error get data from ajax');
        }
    });
}

function reload_table_detail() {
    table_detail.ajax.reload(null, false);
}

function save_detail() {
    $('#btnSaveDetail').text('saving...');
    $('#btnSaveDetail').attr('disabled', true);
    var url;

    if (save_method_detail == 'add') {
        url = "<?= base_url('purchase_invoices/insert_detail') ?>";
    } else {
        url = "<?= base_url('purchase_invoices/update_detail') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form_detail').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status) {
                $('#modal_form_detail').modal('hide');
                reload_table_detail();
                toastr.success('Item berhasil disimpan');
            }
            $('#btnSaveDetail').text('save');
            $('#btnSaveDetail').attr('disabled', false);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error adding / update data');
            $('#btnSaveDetail').text('save');
            $('#btnSaveDetail').attr('disabled', false);
        }
    });
}

function hapus_detail(id) {
    if (confirm('Are you sure delete this item?')) {
        $.ajax({
            url: "<?= base_url('purchase_invoices/delete_detail') ?>",
            type: "POST",
            dataType: "JSON",
            data: {
                id: id,
                purchase_invoice_id: purchase_invoice_id
            },
            success: function(data) {
                reload_table_detail();
                toastr.success('Item berhasil dihapus');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error deleting data');
            }
        });
    }
}

function updatePurchaseInvoiceSummary() {
    $.ajax({
        url: "<?= base_url('purchase_invoices/get_purchase_invoice_summary') ?>",
        type: "POST",
        data: {
            purchase_invoice_id: purchase_invoice_id
        },
        dataType: "JSON",
        success: function(data) {
            if (data) {
                $('#items_subtotal').text('Rp ' + number_format(data.subtotal_amount || 0));
                calculateTotal();
            }
        }
    });
}

function calculateTotal() {
    var items_subtotal = parseFloat($('#items_subtotal').text().replace(/[^0-9.-]+/g, "")) || 0;
    var discount_type = "<?= $purchase_invoice->discount_type ?>";
    var discount_value = <?= $purchase_invoice->discount_value ?>;
    var tax_percentage = <?= $purchase_invoice->tax_percentage ?>;
    var shipping_cost = <?= $purchase_invoice->shipping_cost ?>;

    // Calculate discount
    var discount_amount = 0;
    if (discount_type == 'percentage') {
        discount_amount = (items_subtotal * discount_value) / 100;
    } else {
        discount_amount = discount_value;
    }

    // Calculate tax
    var tax_amount = ((items_subtotal - discount_amount) * tax_percentage) / 100;

    // Calculate total
    var total_amount = items_subtotal - discount_amount + tax_amount + shipping_cost;

    // Update display
    $('#discount_amount').text('Rp ' + number_format(discount_amount));
    $('#tax_amount').text('Rp ' + number_format(tax_amount));
    $('#total_amount').text('Rp ' + number_format(total_amount));

    // Calculate remaining amount
    var paid_amount = <?= $purchase_invoice->paid_amount ?>;
    var remaining_amount = total_amount - paid_amount;
    $('#remaining_amount').text('Rp ' + number_format(remaining_amount));
}

function number_format(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}
</script>

<style>
.toggle-icon {
    transition: transform 0.3s ease;
}
.toggle-icon.rotated {
    transform: rotate(180deg);
}
</style>
