<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-list text-blue"></i> Data Purchase Payments</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_purchase_payments" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Payment No</th>
                                    <th>Payment Date</th>
                                    <th>Supplier</th>
                                    <th>Payment Method</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<!-- Modal Form -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Purchase Payment Form</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body form">
                <form action="#" id="form" class="form-horizontal">
                    <input type="hidden" value="" name="id" />
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Supplier <span class="text-danger">*</span></label>
                                    <select class="form-control" name="supplier_id" id="supplier_id" required>
                                        <option value="">-- Select Supplier --</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Payment Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" name="payment_date" required>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Payment Method <span class="text-danger">*</span></label>
                                    <select class="form-control" name="payment_method" id="payment_method" required>
                                        <option value="">-- Select Method --</option>
                                        <option value="cash">Cash</option>
                                        <option value="bank_transfer">Bank Transfer</option>
                                        <option value="check">Check</option>
                                        <option value="credit_card">Credit Card</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Bank Account</label>
                                    <input type="text" class="form-control" name="bank_account" placeholder="Bank account details (if applicable)">
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Reference No</label>
                                    <input type="text" class="form-control" name="reference_no" placeholder="Reference number">
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-control" name="status" required>
                                        <option value="">-- Select Status --</option>
                                        <option value="draft">Draft</option>
                                        <option value="completed">Completed</option>
                                        <option value="cancelled">Cancelled</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Total Amount <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" name="total_amount" step="0.01" min="0" required>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Notes</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="Payment notes"></textarea>
                            <span class="help-block"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script type="text/javascript">
var save_method;
var table;

$(document).ready(function() {
    //datatables
    table = $("#tbl_purchase_payments").DataTable({
        "responsive": true,
        "autoWidth": false,
        "language": {
            "sEmptyTable": "Belum Ada Data Purchase Payments"
        },
        "processing": true,
        "serverSide": true,
        "order": [],

        "ajax": {
            "url": "<?= base_url('purchase_payments/ajax_list') ?>",
            "type": "POST"
        },

        "columnDefs": [{
            "targets": [-1],
            "orderable": false,
        }, ],

    });

    // Load suppliers for dropdown
    loadSuppliers();
});

function loadSuppliers() {
    $.ajax({
        url: "<?= base_url('purchase_payments/get_suppliers') ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            var options = '<option value="">-- Select Supplier --</option>';
            $.each(data, function(index, supplier) {
                options += '<option value="' + supplier.id + '">' + supplier.nama + '</option>';
            });
            $('#supplier_id').html(options);
        }
    });
}

function add() {
    save_method = 'add';
    $('#form')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('#modal_form').modal('show');
    $('.modal-title').text('Add Purchase Payment');
    
    // Set default date to today
    $('[name="payment_date"]').val(new Date().toISOString().split('T')[0]);
}

function edit(id) {
    save_method = 'update';
    $('#form')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    $.ajax({
        url: "<?= base_url('purchase_payments/edit/') ?>" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('[name="id"]').val(data.id);
            $('[name="supplier_id"]').val(data.supplier_id);
            $('[name="payment_date"]').val(data.payment_date);
            $('[name="payment_method"]').val(data.payment_method);
            $('[name="bank_account"]').val(data.bank_account);
            $('[name="reference_no"]').val(data.reference_no);
            $('[name="notes"]').val(data.notes);
            $('[name="total_amount"]').val(data.total_amount);
            $('[name="status"]').val(data.status);

            $('#modal_form').modal('show');
            $('.modal-title').text('Edit Purchase Payment');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error get data from ajax');
        }
    });
}

function reload_table() {
    table.ajax.reload(null, false);
}

function save() {
    $('#btnSave').text('saving...');
    $('#btnSave').attr('disabled', true);
    var url;

    if (save_method == 'add') {
        url = "<?= base_url('purchase_payments/add') ?>";
    } else {
        url = "<?= base_url('purchase_payments/update') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status) {
                $('#modal_form').modal('hide');
                reload_table();
                toastr.success('Data berhasil disimpan');
            }
            $('#btnSave').text('save');
            $('#btnSave').attr('disabled', false);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error adding / update data');
            $('#btnSave').text('save');
            $('#btnSave').attr('disabled', false);
        }
    });
}

function hapus(id) {
    if (confirm('Are you sure delete this data?')) {
        $.ajax({
            url: "<?= base_url('purchase_payments/delete') ?>",
            type: "POST",
            dataType: "JSON",
            data: {
                id: id
            },
            success: function(data) {
                if (data.status) {
                    reload_table();
                    toastr.success('Data berhasil dihapus');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error deleting data');
            }
        });
    }
}

// Show/hide bank account field based on payment method
$('#payment_method').change(function() {
    var method = $(this).val();
    if (method === 'bank_transfer') {
        $('[name="bank_account"]').closest('.form-group').show();
        $('[name="bank_account"]').attr('required', true);
    } else {
        $('[name="bank_account"]').closest('.form-group').hide();
        $('[name="bank_account"]').attr('required', false);
        $('[name="bank_account"]').val('');
    }
});
</script>
