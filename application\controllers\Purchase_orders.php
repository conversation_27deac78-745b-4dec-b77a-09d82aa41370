<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Purchase_orders extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_purchase_orders');
        $this->load->model('Mod_supplier');
        $this->load->model('Mod_barang');
    }

    public function index()
    {
        $link = 'purchase_orders';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $this->template->load('layoutbackend', 'purchase_orders/purchase_orders', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_purchase_orders->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->po_no;
            $sub_array[] = date('d-m-Y', strtotime($row->order_date));
            $sub_array[] = $row->supplier_name ? $row->supplier_name : '-';

            // Status badge
            $status_class = $this->getStatusClass($row->status);
            $sub_array[] = "<span class=\"badge badge-{$status_class}\">" . ucfirst($row->status) . "</span>";

            // Priority badge
            $priority_class = $this->getPriorityClass($row->priority);
            $sub_array[] = "<span class=\"badge badge-{$priority_class}\">" . ucfirst($row->priority) . "</span>";

            $sub_array[] = 'Rp ' . number_format($row->total_amount, 0, ',', '.');

            $sub_array[] = "<a class=\"btn btn-xs btn-outline-info detail\" href=\"javascript:void(0)\" title=\"Detail\" onclick=\"detail('$row->id')\"><i class=\"fas fa-list\"></i></a>
                             <a class=\"btn btn-xs btn-outline-primary edit\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"edit('$row->id')\"><i class=\"fas fa-edit\"></i></a>
                             <a class=\"btn btn-xs btn-outline-danger delete\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapus('$row->id')\"><i class=\"fas fa-trash\"></i></a>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_purchase_orders->count_all(),
            "recordsFiltered" => $this->Mod_purchase_orders->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    private function getStatusClass($status)
    {
        switch ($status) {
            case 'draft': return 'secondary';
            case 'ordered': return 'warning';
            case 'received': return 'info';
            case 'invoiced': return 'success';
            default: return 'secondary';
        }
    }

    private function getPriorityClass($priority)
    {
        switch ($priority) {
            case 'low': return 'light';
            case 'normal': return 'primary';
            case 'high': return 'warning';
            case 'urgent': return 'danger';
            default: return 'primary';
        }
    }

    public function insert()
    {
        $save = array(
            'po_no' => $this->input->post('po_no'),
            'supplier_id' => $this->input->post('supplier_id'),
            'order_date' => $this->input->post('order_date'),
            'due_date' => $this->input->post('due_date'),
            'status' => $this->input->post('status') ? $this->input->post('status') : 'draft',
            'priority' => $this->input->post('priority') ? $this->input->post('priority') : 'normal',
            'notes' => $this->input->post('notes'),
            'discount_type' => $this->input->post('discount_type'),
            'discount_value' => $this->input->post('discount_value'),
            'tax_percentage' => $this->input->post('tax_percentage'),
            'shipping_cost' => $this->input->post('shipping_cost'),
            'total_amount' => $this->input->post('total_amount'),
            'created_by' => $this->session->userdata('id_user'),
        );
        $this->Mod_purchase_orders->insert($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $id = $this->input->post('id');
        $save = array(
            'po_no' => $this->input->post('po_no'),
            'supplier_id' => $this->input->post('supplier_id'),
            'order_date' => $this->input->post('order_date'),
            'due_date' => $this->input->post('due_date'),
            'status' => $this->input->post('status'),
            'priority' => $this->input->post('priority'),
            'notes' => $this->input->post('notes'),
            'discount_type' => $this->input->post('discount_type'),
            'discount_value' => $this->input->post('discount_value'),
            'tax_percentage' => $this->input->post('tax_percentage'),
            'shipping_cost' => $this->input->post('shipping_cost'),
            'total_amount' => $this->input->post('total_amount'),
            'updated_by' => $this->session->userdata('id_user'),
        );
        $this->Mod_purchase_orders->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_purchase_orders->get($id);
        echo json_encode($data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_purchase_orders->delete($id, 'purchase_orders');
        echo json_encode(array("status" => TRUE));
    }

    public function detail($id)
    {
        $link = 'purchase_orders';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $data['purchase_order'] = $this->Mod_purchase_orders->get($id);
            $data['purchase_order_id'] = $id;
            $this->template->load('layoutbackend', 'purchase_orders/purchase_order_detail', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function generate_po_number()
    {
        $year = date('Y');
        $month = date('m');

        // Get last PO number for current month
        $last_po = $this->Mod_purchase_orders->get_last_po_number($year, $month);

        if ($last_po) {
            // Extract number from last PO
            $last_number = intval(substr($last_po->po_no, -3));
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }

        $po_number = 'PO-' . $year . $month . '-' . str_pad($new_number, 3, '0', STR_PAD_LEFT);

        echo json_encode(array('po_no' => $po_number));
    }

    public function get_suppliers()
    {
        $suppliers = $this->Mod_supplier->getAll()->result();
        echo json_encode($suppliers);
    }

    public function get_barang()
    {
        $barang = $this->Mod_barang->getAll()->result();
        echo json_encode($barang);
    }

    // Purchase Order Items Management
    public function ajax_list_detail()
    {
        $purchase_order_id = $this->input->post('purchase_order_id');
        $list = $this->Mod_purchase_orders->get_datatables_detail($purchase_order_id);
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->nama;
            $sub_array[] = $row->satuan_nama ? $row->satuan_nama : '-';
            $sub_array[] = '<div class="text-center">' . number_format($row->quantity, 2) . '</div>';
            $sub_array[] = '<div class="text-right">Rp ' . number_format($row->unit_price, 0, ',', '.') . '</div>';
            $sub_array[] = '<div class="text-right">Rp ' . number_format($row->subtotal, 0, ',', '.') . '</div>';
            $sub_array[] = $row->notes ? $row->notes : '-';
            $sub_array[] = "<a class=\"btn btn-xs btn-outline-primary edit\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"editDetail('$row->id')\"><i class=\"fas fa-edit\"></i></a>
                             <a class=\"btn btn-xs btn-outline-danger delete\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapusDetail('$row->id')\"><i class=\"fas fa-trash\"></i></a>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_purchase_orders->count_all_detail($purchase_order_id),
            "recordsFiltered" => $this->Mod_purchase_orders->count_filtered_detail($purchase_order_id),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert_detail()
    {
        $save = array(
            'purchase_order_id' => $this->input->post('purchase_order_id'),
            'id_barang' => $this->input->post('id_barang'),
            'quantity' => $this->input->post('quantity'),
            'unit_price' => $this->input->post('unit_price'),
            'discount' => $this->input->post('discount'),
            'notes' => $this->input->post('notes'),
        );
        $this->Mod_purchase_orders->insert_detail($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $id = $this->input->post('id');
        $save = array(
            'id_barang' => $this->input->post('id_barang'),
            'quantity' => $this->input->post('quantity'),
            'unit_price' => $this->input->post('unit_price'),
            'discount' => $this->input->post('discount'),
            'notes' => $this->input->post('notes'),
        );
        $this->Mod_purchase_orders->update_detail($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_purchase_orders->get_detail($id);
        echo json_encode($data);
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $this->Mod_purchase_orders->delete_detail($id);
        echo json_encode(array("status" => TRUE));
    }

    public function get_purchase_order_summary()
    {
        $purchase_order_id = $this->input->post('purchase_order_id');
        $summary = $this->Mod_purchase_orders->get_purchase_order_summary($purchase_order_id);
        echo json_encode($summary);
    }

    public function calculate_total()
    {
        $purchase_order_id = $this->input->post('purchase_order_id');
        $purchase_order = $this->Mod_purchase_orders->get($purchase_order_id);
        $summary = $this->Mod_purchase_orders->get_purchase_order_summary($purchase_order_id);

        $subtotal = $summary['grand_total'];
        $discount_amount = 0;

        // Calculate discount
        if ($purchase_order->discount_type == 'percentage') {
            $discount_amount = ($subtotal * $purchase_order->discount_value) / 100;
        } elseif ($purchase_order->discount_type == 'fixed') {
            $discount_amount = $purchase_order->discount_value;
        }

        $after_discount = $subtotal - $discount_amount;
        $tax_amount = ($after_discount * $purchase_order->tax_percentage) / 100;
        $total_amount = $after_discount + $tax_amount + $purchase_order->shipping_cost;

        // Update total amount in purchase order
        $this->Mod_purchase_orders->update($purchase_order_id, array('total_amount' => $total_amount));

        $result = array(
            'subtotal' => $subtotal,
            'discount_amount' => $discount_amount,
            'after_discount' => $after_discount,
            'tax_amount' => $tax_amount,
            'shipping_cost' => $purchase_order->shipping_cost,
            'total_amount' => $total_amount
        );

        echo json_encode($result);
    }
}
