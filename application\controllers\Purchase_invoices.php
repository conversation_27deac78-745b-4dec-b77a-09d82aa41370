<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Purchase_invoices extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_purchase_invoices');
        $this->load->model('Mod_supplier');
        $this->load->model('Mod_barang');
        $this->load->model('Mod_purchase_orders');
    }

    public function index()
    {
        $link = 'purchase_invoices';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $this->template->load('layoutbackend', 'purchase_invoices/purchase_invoices', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $list = $this->Mod_purchase_invoices->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->invoice_no;
            $sub_array[] = date('d-m-Y', strtotime($row->invoice_date));
            $sub_array[] = $row->supplier_name ? $row->supplier_name : '-';
            $sub_array[] = $row->po_no ? $row->po_no : '-';
            
            // Status badge
            $status_class = '';
            switch ($row->status) {
                case 'paid':
                    $status_class = 'badge-success';
                    break;
                case 'partial':
                    $status_class = 'badge-warning';
                    break;
                case 'unpaid':
                    $status_class = 'badge-danger';
                    break;
                default:
                    $status_class = 'badge-secondary';
            }
            $sub_array[] = "<span class='badge $status_class'>" . ucfirst($row->status) . "</span>";
            
            $sub_array[] = 'Rp ' . number_format($row->total_amount, 0, ',', '.');

            $sub_array[] = "<a class=\"btn btn-xs btn-outline-info detail\" href=\"" . base_url('purchase_invoices/detail/' . $row->id) . "\" title=\"Detail\"><i class=\"fas fa-list\"></i></a>
                             <a class=\"btn btn-xs btn-outline-primary edit\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"edit('$row->id')\"><i class=\"fas fa-edit\"></i></a>
                             <a class=\"btn btn-xs btn-outline-danger delete\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapus('$row->id')\"><i class=\"fas fa-trash\"></i></a>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_purchase_invoices->count_all(),
            "recordsFiltered" => $this->Mod_purchase_invoices->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function get_suppliers()
    {
        $suppliers = $this->Mod_supplier->getAll()->result();
        echo json_encode($suppliers);
    }

    public function get_purchase_orders()
    {
        $purchase_orders = $this->db->select('id, po_no, supplier_id, order_date, total_amount')
                                   ->from('purchase_orders')
                                   ->where('status !=', 'draft')
                                   ->order_by('order_date', 'desc')
                                   ->get()->result();
        echo json_encode($purchase_orders);
    }

    public function get_barang()
    {
        $barang = $this->Mod_barang->getAll()->result();
        echo json_encode($barang);
    }

    public function generate_invoice_number()
    {
        $year = date('Y');
        $month = date('m');
        
        $last_invoice = $this->Mod_purchase_invoices->get_last_invoice_number($year, $month);
        
        if ($last_invoice) {
            $last_number = substr($last_invoice->invoice_no, -3);
            $new_number = str_pad((int)$last_number + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $new_number = '001';
        }
        
        $invoice_no = 'PI-' . $year . $month . '-' . $new_number;
        echo json_encode(array('invoice_no' => $invoice_no));
    }

    public function insert()
    {
        $save = array(
            'invoice_no' => $this->input->post('invoice_no'),
            'supplier_id' => $this->input->post('supplier_id'),
            'po_id' => $this->input->post('po_id'),
            'invoice_date' => $this->input->post('invoice_date'),
            'due_date' => $this->input->post('due_date'),
            'reference_no' => $this->input->post('reference_no'),
            'notes' => $this->input->post('notes'),
            'discount_type' => $this->input->post('discount_type'),
            'discount_value' => $this->input->post('discount_value'),
            'tax_percentage' => $this->input->post('tax_percentage'),
            'shipping_cost' => $this->input->post('shipping_cost'),
            'total_amount' => $this->input->post('total_amount'),
            'paid_amount' => $this->input->post('paid_amount') ? $this->input->post('paid_amount') : 0,
            'status' => $this->input->post('status') ? $this->input->post('status') : 'unpaid',
            'created_by' => $this->session->userdata('id_user'),
            'updated_by' => $this->session->userdata('id_user'),
        );
        $this->Mod_purchase_invoices->insert($save, 'purchase_invoices');
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $id = $this->input->post('id');
        $save = array(
            'invoice_no' => $this->input->post('invoice_no'),
            'supplier_id' => $this->input->post('supplier_id'),
            'po_id' => $this->input->post('po_id'),
            'invoice_date' => $this->input->post('invoice_date'),
            'due_date' => $this->input->post('due_date'),
            'reference_no' => $this->input->post('reference_no'),
            'notes' => $this->input->post('notes'),
            'discount_type' => $this->input->post('discount_type'),
            'discount_value' => $this->input->post('discount_value'),
            'tax_percentage' => $this->input->post('tax_percentage'),
            'shipping_cost' => $this->input->post('shipping_cost'),
            'total_amount' => $this->input->post('total_amount'),
            'paid_amount' => $this->input->post('paid_amount'),
            'status' => $this->input->post('status'),
            'updated_by' => $this->session->userdata('id_user'),
        );
        $this->Mod_purchase_invoices->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_purchase_invoices->get($id);
        echo json_encode($data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_purchase_invoices->delete($id, 'purchase_invoices');
        echo json_encode(array("status" => TRUE));
    }

    public function detail($id)
    {
        $link = 'purchase_invoices';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $data['purchase_invoice'] = $this->Mod_purchase_invoices->get($id);
            $data['purchase_invoice_id'] = $id;
            $this->template->load('layoutbackend', 'purchase_invoices/purchase_invoice_detail', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    // Purchase Invoice Detail Methods
    public function ajax_list_detail()
    {
        $list = $this->Mod_purchase_invoices->get_datatables_detail();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->barang_nama;
            $sub_array[] = number_format($row->quantity, 2);
            $sub_array[] = 'Rp ' . number_format($row->unit_price, 0, ',', '.');
            $sub_array[] = 'Rp ' . number_format($row->discount, 0, ',', '.');
            $sub_array[] = 'Rp ' . number_format($row->subtotal, 0, ',', '.');
            $sub_array[] = $row->notes ? $row->notes : '-';

            $sub_array[] = "<a class=\"btn btn-xs btn-outline-primary edit-detail\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"edit_detail('$row->id')\"><i class=\"fas fa-edit\"></i></a>
                             <a class=\"btn btn-xs btn-outline-danger delete-detail\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapus_detail('$row->id')\"><i class=\"fas fa-trash\"></i></a>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_purchase_invoices->count_all_detail(),
            "recordsFiltered" => $this->Mod_purchase_invoices->count_filtered_detail(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert_detail()
    {
        $save = array(
            'purchase_invoice_id' => $this->input->post('purchase_invoice_id'),
            'id_barang' => $this->input->post('id_barang'),
            'quantity' => $this->input->post('quantity'),
            'unit_price' => $this->input->post('unit_price'),
            'discount' => $this->input->post('discount'),
            'notes' => $this->input->post('notes'),
        );
        $this->Mod_purchase_invoices->insert_detail($save);
        
        // Update purchase invoice total
        $this->Mod_purchase_invoices->update_purchase_invoice_total($this->input->post('purchase_invoice_id'));
        
        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $id = $this->input->post('id');
        $save = array(
            'id_barang' => $this->input->post('id_barang'),
            'quantity' => $this->input->post('quantity'),
            'unit_price' => $this->input->post('unit_price'),
            'discount' => $this->input->post('discount'),
            'notes' => $this->input->post('notes'),
        );
        $this->Mod_purchase_invoices->update_detail($id, $save);
        
        // Update purchase invoice total
        $this->Mod_purchase_invoices->update_purchase_invoice_total($this->input->post('purchase_invoice_id'));
        
        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_purchase_invoices->get_detail($id);
        echo json_encode($data);
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $purchase_invoice_id = $this->input->post('purchase_invoice_id');
        
        $this->Mod_purchase_invoices->delete_detail($id);
        
        // Update purchase invoice total
        $this->Mod_purchase_invoices->update_purchase_invoice_total($purchase_invoice_id);
        
        echo json_encode(array("status" => TRUE));
    }

    public function get_purchase_invoice_summary()
    {
        $purchase_invoice_id = $this->input->post('purchase_invoice_id');
        $summary = $this->Mod_purchase_invoices->get_purchase_invoice_summary($purchase_invoice_id);
        echo json_encode($summary);
    }
}
