-- =====================================================
-- PURCHASE ORDER MODULE DATABASE SCHEMA
-- =====================================================

-- Tabel Purchase Orders (disesuaikan dengan struktur aplikasi yang ada)
CREATE TABLE IF NOT EXISTS `purchase_orders` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `po_no` varchar(50) NOT NULL,
    `supplier_id` int(11) DEFAULT NULL,
    `order_date` date NOT NULL,
    `due_date` date DEFAULT NULL,
    `status` enum('draft','ordered','received','invoiced') DEFAULT 'draft',
    `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
    `notes` text DEFAULT NULL,
    `discount_type` enum('percentage','fixed') DEFAULT 'percentage',
    `discount_value` decimal(15,2) DEFAULT 0.00,
    `tax_percentage` decimal(5,2) DEFAULT 0.00,
    `shipping_cost` decimal(15,2) DEFAULT 0.00,
    `total_amount` decimal(15,2) DEFAULT 0.00,
    `created_by` int(11) DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `supplier_id` (`supplier_id`),
    KEY `po_no` (`po_no`),
    KEY `order_date` (`order_date`),
    KEY `status` (`status`),
    CONSTRAINT `purchase_orders_ibfk_1` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Tabel Purchase Order Items
CREATE TABLE IF NOT EXISTS `purchase_order_items` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `purchase_order_id` bigint(20) NOT NULL,
    `id_barang` int(11) NOT NULL,
    `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
    `unit_price` decimal(15,2) NOT NULL DEFAULT 0.00,
    `discount` decimal(15,2) DEFAULT 0.00,
    `subtotal` decimal(15,2) GENERATED ALWAYS AS ((`quantity` * `unit_price`) - `discount`) STORED,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `purchase_order_id` (`purchase_order_id`),
    KEY `id_barang` (`id_barang`),
    CONSTRAINT `purchase_order_items_ibfk_1` FOREIGN KEY (`purchase_order_id`) REFERENCES `purchase_orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `purchase_order_items_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Sample Data untuk Purchase Orders
INSERT INTO `purchase_orders` (`po_no`, `supplier_id`, `order_date`, `due_date`, `status`, `priority`, `notes`, `total_amount`, `created_by`) VALUES
('PO-2025-001', 2, '2025-01-15', '2025-01-30', 'draft', 'normal', 'Purchase order untuk stok bulanan elektronik', 1500000.00, 1),
('PO-2025-002', 2, '2025-01-16', '2025-02-01', 'ordered', 'high', 'Purchase order urgent untuk proyek khusus', 2750000.00, 1),
('PO-2025-003', 2, '2025-01-17', '2025-02-15', 'received', 'normal', 'Purchase order untuk restok barang laris', 980000.00, 1);

-- Sample Data untuk Purchase Order Items
INSERT INTO `purchase_order_items` (`purchase_order_id`, `id_barang`, `quantity`, `unit_price`, `discount`, `notes`) VALUES
(1, 1, 50.00, 8000.00, 0.00, 'Barang elektronik untuk stok reguler'),
(1, 4, 20.00, 90000.00, 50000.00, 'Diskon khusus dari supplier'),
(2, 1, 100.00, 8000.00, 0.00, 'Order dalam jumlah besar untuk proyek'),
(2, 4, 30.00, 90000.00, 0.00, 'Untuk proyek khusus pelanggan'),
(3, 1, 25.00, 8000.00, 0.00, 'Restok barang laris'),
(3, 4, 15.00, 90000.00, 25000.00, 'Diskon volume pembelian');

-- Menambahkan submenu Purchase Orders ke menu Purchase
INSERT INTO `tbl_submenu` (`nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES
('Purchase Orders', 'purchase_orders', 'far fa-circle', 7, 'Y', 1);

-- Menambahkan akses untuk semua level user (sesuaikan dengan kebutuhan)
INSERT INTO `tbl_akses_submenu` (`id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `import`) 
SELECT 
    ul.id_level,
    (SELECT id_submenu FROM tbl_submenu WHERE link = 'purchase_orders' LIMIT 1),
    'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'
FROM tbl_user_level ul;
