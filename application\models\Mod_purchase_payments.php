<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Mod_purchase_payments extends CI_Model
{
    var $table = 'purchase_payments';
    var $column_search = array('payment_no', 'payment_date', 's.nama', 'payment_method', 'status', 'total_amount');
    var $column_order = array('payment_no', 'payment_date', 's.nama', 'payment_method', 'status', 'total_amount');
    var $order = array('id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('pp.*, s.nama as supplier_name, s.alamat as supplier_address, s.no_telepon as supplier_phone');
        $this->db->from($this->table . ' pp');
        $this->db->join('supplier s', 's.id = pp.supplier_id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    function save($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    function get($id)
    {
        $this->db->select('pp.*, s.nama as supplier_name, s.alamat as supplier_address, s.no_telepon as supplier_phone');
        $this->db->from($this->table . ' pp');
        $this->db->join('supplier s', 's.id = pp.supplier_id', 'left');
        $this->db->where('pp.id', $id);
        return $this->db->get()->row();
    }

    function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
    }

    function get_last_payment_number($year, $month)
    {
        $this->db->select('payment_no');
        $this->db->from($this->table);
        $this->db->like('payment_no', 'PP-' . $year . $month, 'after');
        $this->db->order_by('payment_no', 'desc');
        $this->db->limit(1);
        return $this->db->get()->row();
    }

    // Methods for purchase payment detail
    var $table_detail = 'purchase_payment_items';
    var $column_search_detail = array('pi.invoice_no', 's.nama', 'ppi.amount_paid', 'ppi.notes');
    var $column_order_detail = array('pi.invoice_no', 's.nama', 'pi.total_amount', 'ppi.amount_paid', 'ppi.notes');
    var $order_detail = array('ppi.id' => 'desc');

    private function _get_datatables_query_detail($purchase_payment_id)
    {
        $this->db->select('ppi.*, pi.invoice_no, pi.total_amount as invoice_total, s.nama as supplier_name');
        $this->db->from($this->table_detail . ' ppi');
        $this->db->join('purchase_invoices pi', 'pi.id = ppi.purchase_invoice_id', 'left');
        $this->db->join('supplier s', 's.id = pi.supplier_id', 'left');
        $this->db->where('ppi.purchase_payment_id', $purchase_payment_id);

        $i = 0;
        foreach ($this->column_search_detail as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search_detail) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order_detail[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order_detail)) {
            $order = $this->order_detail;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables_detail($purchase_payment_id)
    {
        $this->_get_datatables_query_detail($purchase_payment_id);
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_detail($purchase_payment_id)
    {
        $this->_get_datatables_query_detail($purchase_payment_id);
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all_detail($purchase_payment_id)
    {
        $this->db->from($this->table_detail);
        $this->db->where('purchase_payment_id', $purchase_payment_id);
        return $this->db->count_all_results();
    }

    function save_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    function get_detail($id)
    {
        $this->db->select('ppi.*, pi.invoice_no, pi.total_amount as invoice_total, s.nama as supplier_name');
        $this->db->from($this->table_detail . ' ppi');
        $this->db->join('purchase_invoices pi', 'pi.id = ppi.purchase_invoice_id', 'left');
        $this->db->join('supplier s', 's.id = pi.supplier_id', 'left');
        $this->db->where('ppi.id', $id);
        return $this->db->get()->row();
    }

    // Helper methods
    function get_unpaid_invoices()
    {
        $this->db->select('pi.*, s.nama as supplier_name, (pi.total_amount - pi.paid_amount) as remaining_amount');
        $this->db->from('purchase_invoices pi');
        $this->db->join('supplier s', 's.id = pi.supplier_id', 'left');
        $this->db->where('pi.status !=', 'paid');
        $this->db->order_by('pi.invoice_date', 'desc');
        return $this->db->get()->result();
    }

    function get_unpaid_invoices_by_supplier($supplier_id)
    {
        $this->db->select('pi.*, s.nama as supplier_name, (pi.total_amount - pi.paid_amount) as remaining_amount');
        $this->db->from('purchase_invoices pi');
        $this->db->join('supplier s', 's.id = pi.supplier_id', 'left');
        $this->db->where('pi.supplier_id', $supplier_id);
        $this->db->where('pi.status !=', 'paid');
        $this->db->order_by('pi.invoice_date', 'desc');
        return $this->db->get()->result();
    }

    function get_payment_summary($purchase_payment_id)
    {
        $this->db->select('SUM(amount_paid) as total_allocated');
        $this->db->from($this->table_detail);
        $this->db->where('purchase_payment_id', $purchase_payment_id);
        return $this->db->get()->row();
    }

    function get_payment_items($purchase_payment_id)
    {
        $this->db->select('ppi.*, pi.invoice_no, pi.total_amount as invoice_total, s.nama as supplier_name');
        $this->db->from($this->table_detail . ' ppi');
        $this->db->join('purchase_invoices pi', 'pi.id = ppi.purchase_invoice_id', 'left');
        $this->db->join('supplier s', 's.id = pi.supplier_id', 'left');
        $this->db->where('ppi.purchase_payment_id', $purchase_payment_id);
        $this->db->order_by('ppi.id', 'asc');
        return $this->db->get()->result();
    }

    // Update total amount in payment header based on payment items
    function update_payment_total($purchase_payment_id)
    {
        $summary = $this->get_payment_summary($purchase_payment_id);
        $total_amount = $summary ? $summary->total_allocated : 0;
        
        $this->db->where('id', $purchase_payment_id);
        $this->db->update($this->table, array('total_amount' => $total_amount));
    }
}
