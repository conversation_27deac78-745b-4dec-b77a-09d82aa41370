<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Purchase_payments extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_purchase_payments');
        $this->load->model('Mod_dashboard');
        $this->load->library('template');
    }

    public function index()
    {
        $link = 'purchase_payments';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $this->template->load('layoutbackend', 'purchase_payments/purchase_payments', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $list = $this->Mod_purchase_payments->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->payment_no;
            $sub_array[] = date('d-m-Y', strtotime($row->payment_date));
            $sub_array[] = $row->supplier_name ? $row->supplier_name : '-';
            $sub_array[] = ucfirst(str_replace('_', ' ', $row->payment_method));
            
            // Status badge
            $status_class = '';
            switch ($row->status) {
                case 'completed':
                    $status_class = 'badge-success';
                    break;
                case 'draft':
                    $status_class = 'badge-warning';
                    break;
                case 'cancelled':
                    $status_class = 'badge-danger';
                    break;
                default:
                    $status_class = 'badge-secondary';
            }
            $sub_array[] = '<span class="badge ' . $status_class . '">' . ucfirst($row->status) . '</span>';
            
            $sub_array[] = 'Rp ' . number_format($row->total_amount, 0, ',', '.');
            
            // Action buttons
            $aksi = '';
            $aksi .= '<a href="' . base_url('purchase_payments/detail/' . $row->id) . '" class="btn btn-sm btn-info" title="Detail"><i class="fa fa-eye"></i></a> ';
            $aksi .= '<button type="button" onclick="edit(' . $row->id . ')" class="btn btn-sm btn-warning" title="Edit"><i class="fa fa-edit"></i></button> ';
            $aksi .= '<button type="button" onclick="hapus(' . $row->id . ')" class="btn btn-sm btn-danger" title="Delete"><i class="fa fa-trash"></i></button>';
            $sub_array[] = $aksi;
            
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_purchase_payments->count_all(),
            "recordsFiltered" => $this->Mod_purchase_payments->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function add()
    {
        // Generate payment number
        $year = date('Y');
        $month = date('m');
        $last_payment = $this->Mod_purchase_payments->get_last_payment_number($year, $month);
        
        if ($last_payment) {
            $last_number = intval(substr($last_payment->payment_no, -3));
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        $payment_no = 'PP-' . $year . $month . '-' . str_pad($new_number, 3, '0', STR_PAD_LEFT);

        $save = array(
            'payment_no' => $payment_no,
            'supplier_id' => $this->input->post('supplier_id'),
            'payment_date' => $this->input->post('payment_date'),
            'payment_method' => $this->input->post('payment_method'),
            'bank_account' => $this->input->post('bank_account'),
            'reference_no' => $this->input->post('reference_no'),
            'notes' => $this->input->post('notes'),
            'total_amount' => $this->input->post('total_amount'),
            'status' => $this->input->post('status'),
            'created_by' => $this->session->userdata('id_user'),
        );
        $this->Mod_purchase_payments->save($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $id = $this->input->post('id');
        $save = array(
            'supplier_id' => $this->input->post('supplier_id'),
            'payment_date' => $this->input->post('payment_date'),
            'payment_method' => $this->input->post('payment_method'),
            'bank_account' => $this->input->post('bank_account'),
            'reference_no' => $this->input->post('reference_no'),
            'notes' => $this->input->post('notes'),
            'total_amount' => $this->input->post('total_amount'),
            'status' => $this->input->post('status'),
            'updated_by' => $this->session->userdata('id_user'),
        );
        $this->Mod_purchase_payments->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_purchase_payments->get($id);
        echo json_encode($data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_purchase_payments->delete($id, 'purchase_payments');
        echo json_encode(array("status" => TRUE));
    }

    public function detail($id)
    {
        $link = 'purchase_payments';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $data['purchase_payment'] = $this->Mod_purchase_payments->get($id);
            $data['purchase_payment_id'] = $id;
            $this->template->load('layoutbackend', 'purchase_payments/purchase_payment_detail', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    // Methods for purchase payment detail (payment items)
    public function ajax_list_detail()
    {
        $purchase_payment_id = $this->input->post('purchase_payment_id');
        $list = $this->Mod_purchase_payments->get_datatables_detail($purchase_payment_id);
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->invoice_no;
            $sub_array[] = $row->supplier_name ? $row->supplier_name : '-';
            $sub_array[] = 'Rp ' . number_format($row->invoice_total, 0, ',', '.');
            $sub_array[] = 'Rp ' . number_format($row->amount_paid, 0, ',', '.');
            $sub_array[] = $row->notes ? $row->notes : '-';
            
            // Action buttons
            $aksi = '';
            $aksi .= '<button type="button" onclick="edit_detail(' . $row->id . ')" class="btn btn-sm btn-warning" title="Edit"><i class="fa fa-edit"></i></button> ';
            $aksi .= '<button type="button" onclick="hapus_detail(' . $row->id . ')" class="btn btn-sm btn-danger" title="Delete"><i class="fa fa-trash"></i></button>';
            $sub_array[] = $aksi;
            
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_purchase_payments->count_all_detail($purchase_payment_id),
            "recordsFiltered" => $this->Mod_purchase_payments->count_filtered_detail($purchase_payment_id),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function add_detail()
    {
        $save = array(
            'purchase_payment_id' => $this->input->post('purchase_payment_id'),
            'purchase_invoice_id' => $this->input->post('purchase_invoice_id'),
            'amount_paid' => $this->input->post('amount_paid'),
            'notes' => $this->input->post('notes'),
        );
        $this->Mod_purchase_payments->save_detail($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $id = $this->input->post('id');
        $save = array(
            'purchase_invoice_id' => $this->input->post('purchase_invoice_id'),
            'amount_paid' => $this->input->post('amount_paid'),
            'notes' => $this->input->post('notes'),
        );
        $this->Mod_purchase_payments->update_detail($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_purchase_payments->get_detail($id);
        echo json_encode($data);
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $this->Mod_purchase_payments->delete($id, 'purchase_payment_items');
        echo json_encode(array("status" => TRUE));
    }

    // Helper methods
    public function get_suppliers()
    {
        $this->load->model('Mod_supplier');
        $suppliers = $this->Mod_supplier->get_all();
        echo json_encode($suppliers);
    }

    public function get_unpaid_invoices($supplier_id = null)
    {
        if ($supplier_id) {
            $invoices = $this->Mod_purchase_payments->get_unpaid_invoices_by_supplier($supplier_id);
        } else {
            $invoices = $this->Mod_purchase_payments->get_unpaid_invoices();
        }
        echo json_encode($invoices);
    }

    public function get_invoice_details($invoice_id)
    {
        $this->load->model('Mod_purchase_invoices');
        $invoice = $this->Mod_purchase_invoices->get($invoice_id);
        echo json_encode($invoice);
    }
}
