-- =============================================
-- Purchase Payments Schema
-- Dibuat untuk modul Purchase Payment yang terintegrasi dengan Purchase Invoice
-- =============================================

-- Tabel Purchase Payments (disesuaikan dengan struktur aplikasi yang ada)
CREATE TABLE IF NOT EXISTS `purchase_payments` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `payment_no` varchar(50) NOT NULL,
    `supplier_id` int(11) DEFAULT NULL,
    `payment_date` date NOT NULL,
    `payment_method` enum('cash','bank_transfer','check','credit_card') DEFAULT 'cash',
    `bank_account` varchar(100) DEFAULT NULL,
    `reference_no` varchar(100) DEFAULT NULL,
    `notes` text DEFAULT NULL,
    `total_amount` decimal(15,2) DEFAULT 0.00,
    `status` enum('draft','completed','cancelled') DEFAULT 'draft',
    `created_by` int(11) DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `payment_no` (`payment_no`),
    KEY `supplier_id` (`supplier_id`),
    KEY `payment_date` (`payment_date`),
    KEY `status` (`status`),
    KEY `created_by` (`created_by`),
    KEY `updated_by` (`updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabel Purchase Payment Items
CREATE TABLE IF NOT EXISTS `purchase_payment_items` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `purchase_payment_id` bigint(20) NOT NULL,
    `purchase_invoice_id` bigint(20) NOT NULL,
    `amount_paid` decimal(15,2) NOT NULL DEFAULT 0.00,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `purchase_payment_id` (`purchase_payment_id`),
    KEY `purchase_invoice_id` (`purchase_invoice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sample Data untuk Purchase Payments
INSERT INTO `purchase_payments` (`payment_no`, `supplier_id`, `payment_date`, `payment_method`, `bank_account`, `reference_no`, `notes`, `total_amount`, `status`, `created_by`, `updated_by`) VALUES
('PP-202501-001', 1, '2025-01-16', 'bank_transfer', 'BCA **********', 'TRF-001', 'Pembayaran untuk invoice PI-202501-001', 1000000.00, 'completed', 1, 1),
('PP-202501-002', 2, '2025-01-21', 'cash', NULL, 'CASH-002', 'Pembayaran tunai untuk invoice PI-202501-002', 3500000.00, 'completed', 1, 1),
('PP-202501-003', 1, '2025-01-25', 'check', NULL, 'CHK-003', 'Pembayaran dengan cek untuk sisa invoice PI-202501-001', 1150000.00, 'draft', 1, 1);

-- Sample Data untuk Purchase Payment Items
INSERT INTO `purchase_payment_items` (`purchase_payment_id`, `purchase_invoice_id`, `amount_paid`, `notes`) VALUES
(1, 1, 1000000.00, 'Pembayaran pertama untuk PI-202501-001'),
(2, 2, 3500000.00, 'Pembayaran lunas untuk PI-202501-002'),
(3, 1, 1150000.00, 'Pelunasan untuk PI-202501-001'),
(3, 3, 500000.00, 'Pembayaran sebagian untuk PI-202501-003');

-- Menambahkan submenu Purchase Payments ke menu Purchase
INSERT INTO `tbl_submenu` (`nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES
('Purchase Payments', 'purchase_payments', 'far fa-circle', 7, 'Y', 3);

-- Menambahkan akses untuk semua level user (sesuaikan dengan kebutuhan)
INSERT INTO `tbl_akses_submenu` (`id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `import`) 
SELECT 
    ul.id_level,
    (SELECT id_submenu FROM tbl_submenu WHERE link = 'purchase_payments' LIMIT 1),
    'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'
FROM tbl_user_level ul;

-- =============================================
-- Trigger untuk auto-update invoice status berdasarkan pembayaran
-- =============================================

DELIMITER $$

CREATE TRIGGER `update_invoice_payment_status_after_insert` 
AFTER INSERT ON `purchase_payment_items` 
FOR EACH ROW 
BEGIN
    DECLARE total_paid DECIMAL(15,2);
    DECLARE invoice_total DECIMAL(15,2);
    
    -- Hitung total yang sudah dibayar untuk invoice ini
    SELECT COALESCE(SUM(amount_paid), 0) INTO total_paid
    FROM purchase_payment_items ppi
    JOIN purchase_payments pp ON pp.id = ppi.purchase_payment_id
    WHERE ppi.purchase_invoice_id = NEW.purchase_invoice_id 
    AND pp.status = 'completed';
    
    -- Ambil total invoice
    SELECT total_amount INTO invoice_total
    FROM purchase_invoices
    WHERE id = NEW.purchase_invoice_id;
    
    -- Update paid_amount dan status di purchase_invoices
    UPDATE purchase_invoices 
    SET 
        paid_amount = total_paid,
        status = CASE 
            WHEN total_paid = 0 THEN 'unpaid'
            WHEN total_paid >= invoice_total THEN 'paid'
            ELSE 'partial'
        END
    WHERE id = NEW.purchase_invoice_id;
END$$

CREATE TRIGGER `update_invoice_payment_status_after_update` 
AFTER UPDATE ON `purchase_payment_items` 
FOR EACH ROW 
BEGIN
    DECLARE total_paid DECIMAL(15,2);
    DECLARE invoice_total DECIMAL(15,2);
    
    -- Hitung total yang sudah dibayar untuk invoice ini
    SELECT COALESCE(SUM(amount_paid), 0) INTO total_paid
    FROM purchase_payment_items ppi
    JOIN purchase_payments pp ON pp.id = ppi.purchase_payment_id
    WHERE ppi.purchase_invoice_id = NEW.purchase_invoice_id 
    AND pp.status = 'completed';
    
    -- Ambil total invoice
    SELECT total_amount INTO invoice_total
    FROM purchase_invoices
    WHERE id = NEW.purchase_invoice_id;
    
    -- Update paid_amount dan status di purchase_invoices
    UPDATE purchase_invoices 
    SET 
        paid_amount = total_paid,
        status = CASE 
            WHEN total_paid = 0 THEN 'unpaid'
            WHEN total_paid >= invoice_total THEN 'paid'
            ELSE 'partial'
        END
    WHERE id = NEW.purchase_invoice_id;
END$$

CREATE TRIGGER `update_invoice_payment_status_after_delete` 
AFTER DELETE ON `purchase_payment_items` 
FOR EACH ROW 
BEGIN
    DECLARE total_paid DECIMAL(15,2);
    DECLARE invoice_total DECIMAL(15,2);
    
    -- Hitung total yang sudah dibayar untuk invoice ini
    SELECT COALESCE(SUM(amount_paid), 0) INTO total_paid
    FROM purchase_payment_items ppi
    JOIN purchase_payments pp ON pp.id = ppi.purchase_payment_id
    WHERE ppi.purchase_invoice_id = OLD.purchase_invoice_id 
    AND pp.status = 'completed';
    
    -- Ambil total invoice
    SELECT total_amount INTO invoice_total
    FROM purchase_invoices
    WHERE id = OLD.purchase_invoice_id;
    
    -- Update paid_amount dan status di purchase_invoices
    UPDATE purchase_invoices 
    SET 
        paid_amount = total_paid,
        status = CASE 
            WHEN total_paid = 0 THEN 'unpaid'
            WHEN total_paid >= invoice_total THEN 'paid'
            ELSE 'partial'
        END
    WHERE id = OLD.purchase_invoice_id;
END$$

DELIMITER ;

-- =============================================
-- Indexes untuk optimasi performance
-- =============================================

CREATE INDEX idx_purchase_payments_supplier_date ON purchase_payments(supplier_id, payment_date);
CREATE INDEX idx_purchase_payments_status_date ON purchase_payments(status, payment_date);
CREATE INDEX idx_purchase_payment_items_payment_invoice ON purchase_payment_items(purchase_payment_id, purchase_invoice_id);

-- =============================================
-- Komentar untuk dokumentasi
-- =============================================

ALTER TABLE `purchase_payments` COMMENT = 'Tabel untuk menyimpan data pembayaran pembelian';
ALTER TABLE `purchase_payment_items` COMMENT = 'Tabel untuk menyimpan detail pembayaran per invoice';
