<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Mod_purchase_invoices extends CI_Model
{
    var $table = 'purchase_invoices';
    var $column_search = array('invoice_no', 'invoice_date', 's.nama', 'po.po_no', 'status', 'total_amount');
    var $column_order = array('invoice_no', 'invoice_date', 's.nama', 'po.po_no', 'status', 'total_amount');
    var $order = array('id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('pi.*, s.nama as supplier_name, po.po_no');
        $this->db->from($this->table . ' pi');
        $this->db->join('supplier s', 's.id = pi.supplier_id', 'left');
        $this->db->join('purchase_orders po', 'po.id = pi.po_id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    function insert($data, $table)
    {
        $this->db->insert($table, $data);
        return $this->db->insert_id();
    }

    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    function get($id)
    {
        $this->db->select('pi.*, s.nama as supplier_name, s.alamat as supplier_address, s.no_telepon as supplier_phone, po.po_no, po.order_date as po_date');
        $this->db->from($this->table . ' pi');
        $this->db->join('supplier s', 's.id = pi.supplier_id', 'left');
        $this->db->join('purchase_orders po', 'po.id = pi.po_id', 'left');
        $this->db->where('pi.id', $id);
        return $this->db->get()->row();
    }

    function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
    }

    function get_last_invoice_number($year, $month)
    {
        $this->db->select('invoice_no');
        $this->db->from($this->table);
        $this->db->like('invoice_no', 'PI-' . $year . $month, 'after');
        $this->db->order_by('invoice_no', 'desc');
        $this->db->limit(1);
        return $this->db->get()->row();
    }

    // Methods for purchase invoice detail
    var $table_detail = 'purchase_invoice_items';
    var $column_search_detail = array('b.nama', 'pii.quantity', 'pii.unit_price', 'pii.discount', 'pii.subtotal', 'pii.notes');
    var $column_order_detail = array('b.nama', 'pii.quantity', 'pii.unit_price', 'pii.discount', 'pii.subtotal', 'pii.notes');
    var $order_detail = array('pii.id' => 'desc');

    private function _get_datatables_query_detail()
    {
        $this->db->select('pii.*, b.nama as barang_nama, b.harga_jual, s.nama as satuan_nama');
        $this->db->from($this->table_detail . ' pii');
        $this->db->join('tbl_barang b', 'b.id_barang = pii.id_barang', 'left');
        $this->db->join('tbl_satuan s', 's.id_satuan = b.id_satuan', 'left');
        $this->db->where('pii.purchase_invoice_id', $_POST['purchase_invoice_id']);

        $i = 0;
        foreach ($this->column_search_detail as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search_detail) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order_detail[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order_detail)) {
            $order = $this->order_detail;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables_detail()
    {
        $this->_get_datatables_query_detail();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_detail()
    {
        $this->_get_datatables_query_detail();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all_detail()
    {
        $this->db->from($this->table_detail);
        $this->db->where('purchase_invoice_id', $_POST['purchase_invoice_id']);
        return $this->db->count_all_results();
    }

    function get_detail($id)
    {
        $this->db->select('pii.*, b.nama as barang_nama, b.harga_jual, s.nama as satuan_nama');
        $this->db->from($this->table_detail . ' pii');
        $this->db->join('tbl_barang b', 'b.id_barang = pii.id_barang', 'left');
        $this->db->join('tbl_satuan s', 's.id_satuan = b.id_satuan', 'left');
        $this->db->where('pii.id', $id);
        return $this->db->get()->row();
    }

    function insert_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    function get_purchase_invoice_summary($purchase_invoice_id)
    {
        $this->db->select('
            COUNT(*) as total_items,
            SUM(quantity) as total_quantity,
            SUM(subtotal) as subtotal_amount
        ');
        $this->db->from($this->table_detail);
        $this->db->where('purchase_invoice_id', $purchase_invoice_id);
        return $this->db->get()->row();
    }

    function update_purchase_invoice_total($purchase_invoice_id)
    {
        $summary = $this->get_purchase_invoice_summary($purchase_invoice_id);
        $purchase_invoice = $this->get($purchase_invoice_id);
        
        if ($summary && $purchase_invoice) {
            $subtotal = $summary->subtotal_amount ? $summary->subtotal_amount : 0;
            
            // Calculate discount
            $discount_amount = 0;
            if ($purchase_invoice->discount_type == 'percentage') {
                $discount_amount = ($subtotal * $purchase_invoice->discount_value) / 100;
            } else {
                $discount_amount = $purchase_invoice->discount_value;
            }
            
            // Calculate tax
            $tax_amount = (($subtotal - $discount_amount) * $purchase_invoice->tax_percentage) / 100;
            
            // Calculate total
            $total_amount = $subtotal - $discount_amount + $tax_amount + $purchase_invoice->shipping_cost;
            
            $this->db->where('id', $purchase_invoice_id);
            $this->db->update($this->table, array('total_amount' => $total_amount));
        }
    }
}
