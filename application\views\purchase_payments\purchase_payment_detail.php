<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Purchase Payment Header -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title">
                            <i class="fas fa-money-bill-wave text-blue"></i>
                            Purchase Payment Detail - <?= $purchase_payment->payment_no ?>
                        </h3>
                        <div class="card-tools">
                            <a href="<?= base_url('purchase_payments') ?>" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5><strong>Payment Information</strong></h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="150"><strong>Payment No:</strong></td>
                                        <td><?= $purchase_payment->payment_no ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Payment Date:</strong></td>
                                        <td><?= date('d-m-Y', strtotime($purchase_payment->payment_date)) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Payment Method:</strong></td>
                                        <td><?= ucfirst(str_replace('_', ' ', $purchase_payment->payment_method)) ?></td>
                                    </tr>
                                    <?php if ($purchase_payment->bank_account): ?>
                                    <tr>
                                        <td><strong>Bank Account:</strong></td>
                                        <td><?= $purchase_payment->bank_account ?></td>
                                    </tr>
                                    <?php endif; ?>
                                    <tr>
                                        <td><strong>Reference No:</strong></td>
                                        <td><?= $purchase_payment->reference_no ? $purchase_payment->reference_no : '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            switch ($purchase_payment->status) {
                                                case 'completed':
                                                    $status_class = 'badge-success';
                                                    break;
                                                case 'draft':
                                                    $status_class = 'badge-warning';
                                                    break;
                                                case 'cancelled':
                                                    $status_class = 'badge-danger';
                                                    break;
                                                default:
                                                    $status_class = 'badge-secondary';
                                            }
                                            ?>
                                            <span class="badge <?= $status_class ?>"><?= ucfirst($purchase_payment->status) ?></span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5><strong>Supplier Information</strong></h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="150"><strong>Supplier:</strong></td>
                                        <td><?= $purchase_payment->supplier_name ? $purchase_payment->supplier_name : '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td><?= $purchase_payment->supplier_address ? $purchase_payment->supplier_address : '-' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td><?= $purchase_payment->supplier_phone ? $purchase_payment->supplier_phone : '-' ?></td>
                                    </tr>
                                </table>

                                <h5><strong>Financial Summary</strong></h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>Total Payment:</strong>
                                            </div>
                                            <div class="col-6 text-right">
                                                <strong class="text-primary">Rp <?= number_format($purchase_payment->total_amount, 0, ',', '.') ?></strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information (Collapsible) -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-outline-info btn-sm" type="button" data-toggle="collapse" data-target="#additionalInfo" aria-expanded="false" aria-controls="additionalInfo">
                                    <i class="fas fa-info-circle toggle-icon"></i> Additional Information
                                </button>
                                <div class="collapse mt-2" id="additionalInfo">
                                    <div class="card card-body bg-light">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <strong>Notes:</strong><br>
                                                <?= $purchase_payment->notes ? nl2br($purchase_payment->notes) : 'No notes available' ?>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <strong>Created:</strong> <?= date('d-m-Y H:i', strtotime($purchase_payment->created_at)) ?>
                                                </small>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <strong>Last Updated:</strong> <?= date('d-m-Y H:i', strtotime($purchase_payment->updated_at)) ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Items -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fas fa-list text-blue"></i> Payment Items</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="add_detail()" title="Add Payment Item">
                                <i class="fas fa-plus"></i> Add Item
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <table id="tbl_purchase_payment_detail" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Invoice No</th>
                                    <th>Supplier</th>
                                    <th>Invoice Total</th>
                                    <th>Amount Paid</th>
                                    <th>Notes</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form for Payment Items -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Payment Item Form</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body form">
                <form action="#" id="form_detail" class="form-horizontal">
                    <input type="hidden" value="" name="id" />
                    <input type="hidden" value="<?= $purchase_payment_id ?>" name="purchase_payment_id" />
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="control-label">Purchase Invoice <span class="text-danger">*</span></label>
                                    <select class="form-control" name="purchase_invoice_id" id="purchase_invoice_id" required>
                                        <option value="">-- Select Invoice --</option>
                                    </select>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Amount Paid <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" name="amount_paid" step="0.01" min="0" required>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label">Invoice Total</label>
                                    <input type="text" class="form-control" id="invoice_total_display" readonly>
                                    <span class="help-block"></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Notes</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="Payment item notes"></textarea>
                            <span class="help-block"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetail" onclick="save_detail()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<style>
.toggle-icon {
    transition: transform 0.3s ease;
}
.toggle-icon.rotated {
    transform: rotate(90deg);
}
</style>

<script type="text/javascript">
var save_method_detail;
var table_detail;
var purchase_payment_id = <?= $purchase_payment_id ?>;

$(document).ready(function() {
    //datatables for purchase payment detail
    table_detail = $("#tbl_purchase_payment_detail").DataTable({
        "responsive": true,
        "autoWidth": false,
        "language": {
            "sEmptyTable": "Belum Ada Item Purchase Payment"
        },
        "processing": true,
        "serverSide": true,
        "order": [],

        "ajax": {
            "url": "<?= base_url('purchase_payments/ajax_list_detail') ?>",
            "type": "POST",
            "data": function(d) {
                d.purchase_payment_id = purchase_payment_id;
            }
        },
        "drawCallback": function(settings) {
            updatePaymentSummary();
        }
    });

    // Load unpaid invoices for dropdown
    loadUnpaidInvoices();

    // Additional Information toggle animation
    $('#additionalInfo').on('show.bs.collapse', function () {
        $('.toggle-icon').addClass('rotated');
    });

    $('#additionalInfo').on('hide.bs.collapse', function () {
        $('.toggle-icon').removeClass('rotated');
    });
});

function loadUnpaidInvoices() {
    var supplier_id = <?= $purchase_payment->supplier_id ? $purchase_payment->supplier_id : 'null' ?>;
    var url = supplier_id ? "<?= base_url('purchase_payments/get_unpaid_invoices/') ?>" + supplier_id : "<?= base_url('purchase_payments/get_unpaid_invoices') ?>";

    $.ajax({
        url: url,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            var options = '<option value="">-- Select Invoice --</option>';
            $.each(data, function(index, invoice) {
                options += '<option value="' + invoice.id + '" data-total="' + invoice.total_amount + '">' +
                          invoice.invoice_no + ' - Rp ' + number_format(invoice.remaining_amount, 0, ',', '.') + ' (remaining)</option>';
            });
            $('#purchase_invoice_id').html(options);
        }
    });
}

function add_detail() {
    save_method_detail = 'add';
    $('#form_detail')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('#modal_form_detail').modal('show');
    $('.modal-title').text('Add Payment Item');
    $('#invoice_total_display').val('');
}

function edit_detail(id) {
    save_method_detail = 'update';
    $('#form_detail')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();

    $.ajax({
        url: "<?= base_url('purchase_payments/edit_detail/') ?>" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('[name="id"]').val(data.id);
            $('[name="purchase_invoice_id"]').val(data.purchase_invoice_id).trigger('change');
            $('[name="amount_paid"]').val(data.amount_paid);
            $('[name="notes"]').val(data.notes);

            $('#modal_form_detail').modal('show');
            $('.modal-title').text('Edit Payment Item');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error get data from ajax');
        }
    });
}

function reload_table_detail() {
    table_detail.ajax.reload(null, false);
}

function save_detail() {
    $('#btnSaveDetail').text('saving...');
    $('#btnSaveDetail').attr('disabled', true);
    var url;

    if (save_method_detail == 'add') {
        url = "<?= base_url('purchase_payments/add_detail') ?>";
    } else {
        url = "<?= base_url('purchase_payments/update_detail') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form_detail').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status) {
                $('#modal_form_detail').modal('hide');
                reload_table_detail();
                toastr.success('Payment item berhasil disimpan');
            }
            $('#btnSaveDetail').text('save');
            $('#btnSaveDetail').attr('disabled', false);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error adding / update data');
            $('#btnSaveDetail').text('save');
            $('#btnSaveDetail').attr('disabled', false);
        }
    });
}

function hapus_detail(id) {
    if (confirm('Are you sure delete this payment item?')) {
        $.ajax({
            url: "<?= base_url('purchase_payments/delete_detail') ?>",
            type: "POST",
            dataType: "JSON",
            data: {
                id: id
            },
            success: function(data) {
                if (data.status) {
                    reload_table_detail();
                    toastr.success('Payment item berhasil dihapus');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error deleting data');
            }
        });
    }
}

// Show invoice total when invoice is selected
$('#purchase_invoice_id').change(function() {
    var selectedOption = $(this).find('option:selected');
    var total = selectedOption.data('total');
    if (total) {
        $('#invoice_total_display').val('Rp ' + number_format(total, 0, ',', '.'));
    } else {
        $('#invoice_total_display').val('');
    }
});

function updatePaymentSummary() {
    // This function can be used to update payment summary if needed
    // For now, it's just a placeholder
}

function number_format(number, decimals, dec_point, thousands_sep) {
    number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
    var n = !isFinite(+number) ? 0 : +number,
        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
        sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
        dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
        s = '',
        toFixedFix = function(n, prec) {
            var k = Math.pow(10, prec);
            return '' + Math.round(n * k) / k;
        };
    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || '').length < prec) {
        s[1] = s[1] || '';
        s[1] += new Array(prec - s[1].length + 1).join('0');
    }
    return s.join(dec);
}
</script>
