-- =============================================
-- Purchase Invoices Schema untuk Toko Elektronik
-- =============================================

-- Tabel Purchase Invoices (disesuaikan dengan struktur aplikasi yang ada)
CREATE TABLE IF NOT EXISTS `purchase_invoices` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `invoice_no` varchar(50) NOT NULL,
    `supplier_id` int(11) DEFAULT NULL,
    `po_id` bigint(20) DEFAULT NULL,
    `invoice_date` date NOT NULL,
    `due_date` date DEFAULT NULL,
    `reference_no` varchar(100) DEFAULT NULL,
    `notes` text DEFAULT NULL,
    `discount_type` enum('percentage','fixed') DEFAULT 'percentage',
    `discount_value` decimal(15,2) DEFAULT 0.00,
    `tax_percentage` decimal(5,2) DEFAULT 0.00,
    `shipping_cost` decimal(15,2) DEFAULT 0.00,
    `total_amount` decimal(15,2) DEFAULT 0.00,
    `paid_amount` decimal(15,2) DEFAULT 0.00,
    `remaining_amount` decimal(15,2) GENERATED ALWAYS AS ((`total_amount` - `paid_amount`)) STORED,
    `status` enum('unpaid','paid','partial') DEFAULT 'unpaid',
    `created_by` int(11) DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `invoice_no` (`invoice_no`),
    KEY `supplier_id` (`supplier_id`),
    KEY `po_id` (`po_id`),
    KEY `created_by` (`created_by`),
    KEY `updated_by` (`updated_by`),
    CONSTRAINT `purchase_invoices_ibfk_1` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `purchase_invoices_ibfk_2` FOREIGN KEY (`po_id`) REFERENCES `purchase_orders` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `purchase_invoices_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `purchase_invoices_ibfk_4` FOREIGN KEY (`updated_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabel Purchase Invoice Items
CREATE TABLE IF NOT EXISTS `purchase_invoice_items` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `purchase_invoice_id` bigint(20) NOT NULL,
    `id_barang` int(11) NOT NULL,
    `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
    `unit_price` decimal(15,2) NOT NULL DEFAULT 0.00,
    `discount` decimal(15,2) DEFAULT 0.00,
    `subtotal` decimal(15,2) GENERATED ALWAYS AS ((`quantity` * `unit_price`) - `discount`) STORED,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `purchase_invoice_id` (`purchase_invoice_id`),
    KEY `id_barang` (`id_barang`),
    CONSTRAINT `purchase_invoice_items_ibfk_1` FOREIGN KEY (`purchase_invoice_id`) REFERENCES `purchase_invoices` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `purchase_invoice_items_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `tbl_barang` (`id_barang`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Sample Data untuk Purchase Invoices
INSERT INTO `purchase_invoices` (`invoice_no`, `supplier_id`, `po_id`, `invoice_date`, `due_date`, `reference_no`, `notes`, `discount_type`, `discount_value`, `tax_percentage`, `shipping_cost`, `total_amount`, `paid_amount`, `status`, `created_by`, `updated_by`) VALUES
('PI-**********', 1, 1, '2025-01-15', '2025-02-14', 'REF-001', 'Invoice untuk PO pertama', 'percentage', 5.00, 11.00, 50000.00, 2150000.00, 1000000.00, 'partial', 1, 1),
('PI-**********', 2, 2, '2025-01-20', '2025-02-19', 'REF-002', 'Invoice untuk PO kedua', 'fixed', 100000.00, 11.00, 75000.00, 3500000.00, 3500000.00, 'paid', 1, 1),
('PI-**********', 1, 3, '2025-01-25', '2025-02-24', 'REF-003', 'Invoice untuk PO ketiga', 'percentage', 3.00, 11.00, 25000.00, 1800000.00, 0.00, 'unpaid', 1, 1);

-- Sample Data untuk Purchase Invoice Items
INSERT INTO `purchase_invoice_items` (`purchase_invoice_id`, `id_barang`, `quantity`, `unit_price`, `discount`, `notes`) VALUES
(1, 1, 50.00, 8000.00, 0.00, 'Barang elektronik untuk stok reguler'),
(1, 4, 20.00, 90000.00, 50000.00, 'Diskon khusus dari supplier'),
(2, 1, 100.00, 8000.00, 0.00, 'Order dalam jumlah besar untuk proyek'),
(2, 4, 30.00, 90000.00, 0.00, 'Untuk proyek khusus pelanggan'),
(3, 1, 25.00, 8000.00, 0.00, 'Restok barang laris'),
(3, 4, 15.00, 90000.00, 25000.00, 'Diskon volume pembelian');

-- Menambahkan submenu Purchase Invoices ke menu Purchase
INSERT INTO `tbl_submenu` (`nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES
('Purchase Invoices', 'purchase_invoices', 'far fa-circle', 7, 'Y', 2);

-- Menambahkan akses untuk semua level user (sesuaikan dengan kebutuhan)
INSERT INTO `tbl_akses_submenu` (`id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `import`) 
SELECT 
    ul.id_level,
    (SELECT id_submenu FROM tbl_submenu WHERE link = 'purchase_invoices' LIMIT 1),
    'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'
FROM tbl_user_level ul;
