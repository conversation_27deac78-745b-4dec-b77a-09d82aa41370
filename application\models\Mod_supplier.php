<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Mod_supplier extends CI_Model
{
    var $table = 'supplier';
    var $column_search = array('id', 'nama', 'no_telepon', 'alamat');
    var $column_order = array('id', 'nama', 'no_telepon', 'alamat');
    var $order = array('id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->from($this->table);
        $i = 0;

        foreach ($this->column_search as $item)
        {
            if ($_POST['search']['value'])
            {
                if ($i === 0)
                {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                }
                else
                {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order']))
        {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        }
        else if (isset($this->order))
        {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    function insert($table,$data)
    {
        return $this->db->insert($table, $data);
    }

    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    function get($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }

    function get_all()
    {
        $this->db->order_by('nama', 'asc');
        return $this->db->get($this->table)->result();
    }

    function delete($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }
}
