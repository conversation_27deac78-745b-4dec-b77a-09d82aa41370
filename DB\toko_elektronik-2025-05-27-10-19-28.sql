-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               8.0.30 - MySQL Community Server - GPL
-- Server OS:                    Win64
-- HeidiSQL Version:             12.1.0.6537
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- Dumping structure for table toko_elektronik.aplikasi
CREATE TABLE IF NOT EXISTS `aplikasi` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `nama_owner` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `alamat` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `tlp` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `brand` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `title` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `nama_aplikasi` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `logo` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `copy_right` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `versi` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `tahun` year DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `nama_pengirim` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `password` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.aplikasi: ~1 rows (approximately)
INSERT INTO `aplikasi` (`id`, `nama_owner`, `alamat`, `tlp`, `brand`, `title`, `nama_aplikasi`, `logo`, `copy_right`, `versi`, `tahun`, `email`, `nama_pengirim`, `password`) VALUES
	(1, 'PT. ABC', 'jalan raya', '085838333009', NULL, 'Toko Elektronik', 'Toko Elektronik', 'Logo.png', 'Copy Right ©', '1.0.0.2', '2025', '<EMAIL>', 'Aryo Coding', 'pfpinffqxutdjexq');

-- Dumping structure for table toko_elektronik.barang
CREATE TABLE IF NOT EXISTS `barang` (
  `id` int NOT NULL AUTO_INCREMENT,
  `barcode` varchar(15) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `kdbarang` varchar(15) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `nama` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `harga` decimal(10,0) DEFAULT NULL,
  `harga_jual` decimal(10,0) DEFAULT NULL,
  `id_satuan` int DEFAULT NULL,
  `perundangan` int DEFAULT NULL,
  `berat` varchar(12) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `rak` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `aktivasi` enum('Ya','Tidak') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Ya',
  `user_input` int DEFAULT NULL,
  `stok` double NOT NULL DEFAULT '0',
  `batch` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `thn_pengadaan` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `no_ijin_edar` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `manufaktur_date` date DEFAULT NULL,
  `nosbbk` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `sumber_dana` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nama_barang` (`nama`),
  KEY `perundangan` (`perundangan`),
  KEY `kemasan` (`id_satuan`),
  CONSTRAINT `barang_ibfk_3` FOREIGN KEY (`id_satuan`) REFERENCES `satuan` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.barang: ~2 rows (approximately)
INSERT INTO `barang` (`id`, `barcode`, `kdbarang`, `nama`, `harga`, `harga_jual`, `id_satuan`, `perundangan`, `berat`, `rak`, `aktivasi`, `user_input`, `stok`, `batch`, `thn_pengadaan`, `no_ijin_edar`, `manufaktur_date`, `nosbbk`, `sumber_dana`) VALUES
	(1, '2341', '2341', 'Barang 1', 8000, NULL, 1, 1, 'gr', 'R1', 'Ya', 1, 0, '', '', '', '0000-00-00', '', ''),
	(4, '1234', '1234', 'Barang 2', 90000, NULL, 1, 1, '12333', 'R1', 'Ya', 1, 0, '', '', '', '0000-00-00', '', '');

-- Dumping structure for table toko_elektronik.gudang
CREATE TABLE IF NOT EXISTS `gudang` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3;

-- Dumping data for table toko_elektronik.gudang: ~0 rows (approximately)
INSERT INTO `gudang` (`id`, `nama`) VALUES
	(3, 'Gudang A');

-- Dumping structure for table toko_elektronik.keluar
CREATE TABLE IF NOT EXISTS `keluar` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_pelanggan` int DEFAULT NULL,
  `tanggal` datetime DEFAULT NULL,
  `user_input` int DEFAULT NULL,
  `id_gudang` int DEFAULT NULL,
  `tgl_input` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `faktur` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_pelanggan` (`id_pelanggan`),
  KEY `user_input` (`user_input`),
  KEY `id_gudang` (`id_gudang`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.keluar: ~2 rows (approximately)
INSERT INTO `keluar` (`id`, `id_pelanggan`, `tanggal`, `user_input`, `id_gudang`, `tgl_input`, `faktur`) VALUES
	(5, 1, '2025-05-24 21:43:52', 1, NULL, '2025-05-24 14:43:52', 'KB-25052400001'),
	(6, 1, '2025-05-24 21:44:25', 1, NULL, '2025-05-24 14:44:25', 'KB-25052400002');

-- Dumping structure for table toko_elektronik.keluar_detail
CREATE TABLE IF NOT EXISTS `keluar_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `id_keluar` int DEFAULT NULL,
  `id_barang` int DEFAULT NULL,
  `jumlah` double DEFAULT '0',
  `sisa` double NOT NULL DEFAULT '0',
  `harga` double NOT NULL DEFAULT '0',
  `harga_jual` double NOT NULL DEFAULT '0',
  `id_satuan` int DEFAULT NULL,
  `id_user` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_barang` (`id_barang`),
  KEY `id_keluar` (`id_keluar`),
  KEY `id_user` (`id_user`),
  CONSTRAINT `keluar_detail_ibfk_1` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `keluar_detail_ibfk_3` FOREIGN KEY (`id_user`) REFERENCES `tbl_user` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.keluar_detail: ~2 rows (approximately)
INSERT INTO `keluar_detail` (`id`, `id_keluar`, `id_barang`, `jumlah`, `sisa`, `harga`, `harga_jual`, `id_satuan`, `id_user`) VALUES
	(4, 5, 1, 2, 0, 8000, 50000, 1, 1),
	(5, 6, 1, 2, 0, 8000, 50000, 1, 1);

-- Dumping structure for table toko_elektronik.orders
CREATE TABLE IF NOT EXISTS `orders` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` varchar(255) NOT NULL,
  `order_date` date NOT NULL,
  `order_notes` text NOT NULL,
  `customer_name` varchar(255) DEFAULT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `customer_address` text,
  `order_status` enum('draft','pending','confirmed','processing','shipped','delivered','cancelled','completed') DEFAULT 'draft',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `due_date` date DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `payment_status` enum('unpaid','partial','paid','refunded') DEFAULT 'unpaid',
  `payment_method` enum('cash','transfer','credit_card','debit_card','e_wallet','other') DEFAULT NULL,
  `discount_type` enum('none','percentage','fixed') DEFAULT 'none',
  `discount_value` decimal(15,2) DEFAULT '0.00',
  `tax_percentage` decimal(5,2) DEFAULT '0.00',
  `shipping_cost` decimal(15,2) DEFAULT '0.00',
  `total_amount` decimal(15,2) DEFAULT '0.00',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `cancelled_reason` text,
  `internal_notes` text,
  PRIMARY KEY (`id`),
  KEY `fk_orders_updated_by` (`updated_by`),
  KEY `fk_orders_approved_by` (`approved_by`),
  KEY `idx_orders_status` (`order_status`),
  KEY `idx_orders_priority` (`priority`),
  KEY `idx_orders_payment_status` (`payment_status`),
  KEY `idx_orders_created_by` (`created_by`),
  KEY `idx_orders_order_date` (`order_date`),
  KEY `idx_orders_due_date` (`due_date`),
  CONSTRAINT `fk_orders_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL,
  CONSTRAINT `fk_orders_created_by` FOREIGN KEY (`created_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL,
  CONSTRAINT `fk_orders_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3;

-- Dumping data for table toko_elektronik.orders: ~2 rows (approximately)
INSERT INTO `orders` (`id`, `order_no`, `order_date`, `order_notes`, `customer_name`, `customer_phone`, `customer_email`, `customer_address`, `order_status`, `priority`, `due_date`, `delivery_date`, `payment_status`, `payment_method`, `discount_type`, `discount_value`, `tax_percentage`, `shipping_cost`, `total_amount`, `created_by`, `updated_by`, `created_at`, `updated_at`, `approved_by`, `approved_at`, `cancelled_reason`, `internal_notes`) VALUES
	(1, '001', '2025-05-24', 'Notes', 'John Doe', '081234567890', '<EMAIL>', 'Jl. Contoh No. 123, Jakarta', 'pending', 'normal', '2025-05-31', NULL, 'unpaid', 'transfer', 'none', 0.00, 10.00, 15000.00, 1665000.00, NULL, NULL, '2025-05-26 03:06:06', '2025-05-26 03:14:27', NULL, NULL, NULL, NULL),
	(2, '002', '2025-05-26', '', 'Herliansyah', '0898766555', '<EMAIL>', 'Lewa XVI Jakarta Timur', 'draft', 'normal', '2025-05-31', '2025-05-26', 'unpaid', 'transfer', 'none', 0.00, 0.00, 0.00, 1880000.00, 1, NULL, '2025-05-26 04:50:13', '2025-05-26 04:58:45', NULL, NULL, NULL, '');

-- Dumping structure for table toko_elektronik.order_detail
CREATE TABLE IF NOT EXISTS `order_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL,
  `id_barang` int NOT NULL,
  `qty` decimal(10,2) NOT NULL DEFAULT '0.00',
  `harga` decimal(15,2) NOT NULL DEFAULT '0.00',
  `keterangan` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `id_barang` (`id_barang`),
  CONSTRAINT `order_detail_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `order_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb3;

-- Dumping data for table toko_elektronik.order_detail: ~4 rows (approximately)
INSERT INTO `order_detail` (`id`, `order_id`, `id_barang`, `qty`, `harga`, `keterangan`, `created_at`, `updated_at`) VALUES
	(3, 1, 1, 15.00, 50000.00, 'Sample item 1', '2025-05-26 02:31:34', '2025-05-26 02:52:12'),
	(4, 1, 4, 10.00, 75000.00, 'Sample item 2', '2025-05-26 02:31:34', '2025-05-26 02:51:05'),
	(6, 2, 1, 10.00, 8000.00, '', '2025-05-26 04:50:50', '2025-05-26 04:58:40'),
	(7, 2, 4, 20.00, 90000.00, '', '2025-05-26 04:51:26', '2025-05-26 04:51:26');

-- Dumping structure for table toko_elektronik.pelanggan
CREATE TABLE IF NOT EXISTS `pelanggan` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) NOT NULL,
  `no_telepon` varchar(255) NOT NULL,
  `alamat` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3;

-- Dumping data for table toko_elektronik.pelanggan: ~1 rows (approximately)
INSERT INTO `pelanggan` (`id`, `nama`, `no_telepon`, `alamat`) VALUES
	(1, 'Pelangan', '12321312', 'tester alamat');

-- Dumping structure for table toko_elektronik.penerimaan
CREATE TABLE IF NOT EXISTS `penerimaan` (
  `id` int NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `tanggal` date DEFAULT NULL,
  `id_supplier` int DEFAULT NULL,
  `user_input` int DEFAULT NULL,
  `id_gudang` int DEFAULT NULL,
  `tgl_input` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sumber` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id_gudang` (`id_gudang`),
  KEY `id_supplier` (`id_supplier`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.penerimaan: ~0 rows (approximately)
INSERT INTO `penerimaan` (`id`, `faktur`, `tanggal`, `id_supplier`, `user_input`, `id_gudang`, `tgl_input`, `sumber`) VALUES
	(3, 'TB-25052400001', '2025-05-24', 2, 1, NULL, '2025-05-24 09:50:40', '');

-- Dumping structure for table toko_elektronik.penerimaan_detail
CREATE TABLE IF NOT EXISTS `penerimaan_detail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_penerimaan` int DEFAULT NULL,
  `id_barang` int DEFAULT NULL,
  `harga` double DEFAULT NULL,
  `jumlah` double DEFAULT NULL,
  `id_satuan` int DEFAULT NULL,
  `id_user` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_penerimaan` (`id_penerimaan`),
  KEY `id_barang` (`id_barang`),
  KEY `kemasan` (`id_satuan`),
  CONSTRAINT `penerimaan_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.penerimaan_detail: ~0 rows (approximately)
INSERT INTO `penerimaan_detail` (`id`, `id_penerimaan`, `id_barang`, `harga`, `jumlah`, `id_satuan`, `id_user`) VALUES
	(1, 3, 1, 8000, 50, 1, 1);

-- Dumping structure for table toko_elektronik.satuan
CREATE TABLE IF NOT EXISTS `satuan` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nama` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.satuan: ~0 rows (approximately)
INSERT INTO `satuan` (`id`, `nama`) VALUES
	(1, 'Strip');

-- Dumping structure for table toko_elektronik.stok_opname
CREATE TABLE IF NOT EXISTS `stok_opname` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_transaksi` int DEFAULT NULL,
  `id_barang` int DEFAULT NULL,
  `tanggal` date DEFAULT NULL,
  `transaksi` enum('Stok Opname','Barang Masuk','Barang Keluar','Koreksi Stok','Retur Keluar','Retur Penerimaan') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `masuk` double NOT NULL DEFAULT '0',
  `keluar` double NOT NULL DEFAULT '0',
  `keterangan` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `user_input` int DEFAULT NULL,
  `id_gudang` int DEFAULT NULL,
  `tgl_input` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `id_barang_fk` (`id_barang`),
  KEY `stok_opname_ibfk_1` (`id_gudang`),
  CONSTRAINT `stok_opname_ibfk_1` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`),
  CONSTRAINT `stok_opname_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.stok_opname: ~3 rows (approximately)
INSERT INTO `stok_opname` (`id`, `id_transaksi`, `id_barang`, `tanggal`, `transaksi`, `masuk`, `keluar`, `keterangan`, `user_input`, `id_gudang`, `tgl_input`) VALUES
	(12, 1, 1, '2025-05-24', 'Barang Masuk', 50, 0, NULL, 1, NULL, '2025-05-24 09:50:40'),
	(15, 4, 1, '2025-05-24', 'Barang Keluar', 0, 2, NULL, 1, NULL, '2025-05-24 14:43:52'),
	(16, 5, 1, '2025-05-24', 'Barang Keluar', 0, 2, NULL, 1, NULL, '2025-05-24 14:44:25');

-- Dumping structure for table toko_elektronik.supplier
CREATE TABLE IF NOT EXISTS `supplier` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) NOT NULL,
  `no_telepon` varchar(255) NOT NULL,
  `alamat` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3;

-- Dumping data for table toko_elektronik.supplier: ~1 rows (approximately)
INSERT INTO `supplier` (`id`, `nama`, `no_telepon`, `alamat`) VALUES
	(2, 'supplier 1', '1234567', 'tester');

-- Dumping structure for table toko_elektronik.tbl_akses_menu
CREATE TABLE IF NOT EXISTS `tbl_akses_menu` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_level` int NOT NULL,
  `id_menu` int NOT NULL,
  `view` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `add` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `edit` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `delete` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `print` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `upload` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `download` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`),
  KEY `id_menu` (`id_menu`),
  KEY `id_level` (`id_level`),
  CONSTRAINT `tbl_akses_menu_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tbl_akses_menu_ibfk_2` FOREIGN KEY (`id_menu`) REFERENCES `tbl_menu` (`id_menu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=432 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.tbl_akses_menu: ~9 rows (approximately)
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES
	(1, 1, 1, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(69, 1, 2, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(94, 1, 3, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(207, 1, 4, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(427, 1, 6, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(428, 1, 7, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(429, 1, 8, 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N'),
	(430, 1, 9, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(431, 1, 10, 'Y', 'N', 'N', 'N', 'N', 'N', 'N');

-- Dumping structure for table toko_elektronik.tbl_akses_submenu
CREATE TABLE IF NOT EXISTS `tbl_akses_submenu` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `id_level` int NOT NULL,
  `id_submenu` int NOT NULL,
  `view` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `add` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `edit` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `delete` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `print` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `upload` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `download` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`),
  KEY `id_level` (`id_level`),
  KEY `id_submenu` (`id_submenu`),
  CONSTRAINT `tbl_akses_submenu_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tbl_akses_submenu_ibfk_2` FOREIGN KEY (`id_submenu`) REFERENCES `tbl_submenu` (`id_submenu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=360 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.tbl_akses_submenu: ~16 rows (approximately)
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES
	(2, 1, 2, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(4, 1, 1, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(6, 1, 3, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(9, 1, 4, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(209, 1, 5, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(289, 1, 6, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(295, 1, 7, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(351, 1, 71, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(352, 1, 72, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(353, 1, 73, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(354, 1, 74, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(355, 1, 75, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(356, 1, 76, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(357, 1, 77, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(358, 1, 78, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(359, 1, 79, 'N', 'N', 'N', 'N', 'N', 'N', 'N');

-- Dumping structure for table toko_elektronik.tbl_menu
CREATE TABLE IF NOT EXISTS `tbl_menu` (
  `id_menu` int NOT NULL AUTO_INCREMENT,
  `nama_menu` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `link` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `icon` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `urutan` bigint DEFAULT NULL,
  `is_active` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Y',
  `parent` enum('Y') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Y',
  PRIMARY KEY (`id_menu`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.tbl_menu: ~9 rows (approximately)
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES
	(1, 'Home', 'dashboard', 'fas fa-tachometer-alt', 0, 'Y', 'Y'),
	(2, 'Konfigurasi', '#', 'fas fa-users-cog', 15, 'Y', 'Y'),
	(3, 'Ganti Password', 'ganti_password', 'fas fa-key', 9, 'Y', 'Y'),
	(4, 'Master', '#', 'fas fa-database', 7, 'Y', 'Y'),
	(6, 'Developer', '#', 'fas fa-tools', 10, 'Y', 'Y'),
	(7, 'Purchase', '#', 'fas fa-credit-card', 3, 'Y', 'Y'),
	(8, 'Sales', '#', 'fas fa-store', 1, 'Y', 'Y'),
	(9, 'Warehouse', '#', 'fas fa-warehouse', 2, 'Y', 'Y'),
	(10, 'Akunting', '#', 'fas fa-receipt', 5, 'Y', 'Y');

-- Dumping structure for table toko_elektronik.tbl_submenu
CREATE TABLE IF NOT EXISTS `tbl_submenu` (
  `id_submenu` int NOT NULL AUTO_INCREMENT,
  `nama_submenu` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `link` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `icon` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `id_menu` int DEFAULT NULL,
  `is_active` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Y',
  `urutan` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id_submenu`),
  KEY `id_menu` (`id_menu`),
  CONSTRAINT `tbl_submenu_ibfk_1` FOREIGN KEY (`id_menu`) REFERENCES `tbl_menu` (`id_menu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.tbl_submenu: ~16 rows (approximately)
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES
	(1, 'Menu', 'menu', 'far fa-circle', 2, 'Y', NULL),
	(2, 'Sub Menu', 'submenu', 'far fa-circle', 2, 'Y', NULL),
	(3, 'Aplikasi', 'aplikasi', 'far fa-circle', 2, 'Y', NULL),
	(4, 'User Level', 'userlevel', 'far fa-circle', 2, 'Y', NULL),
	(5, 'Data Pengguna', 'user', 'far fa-circle', 2, 'Y', NULL),
	(6, 'Barang', 'barang', 'far fa-circle', 4, 'Y', NULL),
	(7, 'Satuan', 'satuan', 'far fa-circle', 4, 'Y', NULL),
	(71, 'Create Tabel', 'TemplateController', 'far fa-circle', 6, 'Y', 1),
	(72, 'Gudang', 'gudang', 'far fa-circle', 4, 'Y', 4),
	(73, 'Pelanggan', 'pelanggan', 'far fa-circle', 4, 'Y', 5),
	(74, 'Suppier', 'supplier', 'far fa-circle', 4, 'Y', 3),
	(75, 'Barang Masuk', 'masuk', 'far fa-circle', 9, 'Y', 1),
	(76, 'Barang Keluar', 'keluar', 'far fa-circle', 9, 'Y', 2),
	(77, 'Stok Opname', 'stok', 'far fa-circle', 9, 'Y', 3),
	(78, 'Orders', 'orders', 'far fa-circle', 8, 'Y', 1),
	(79, 'Purchase Order', 'purchase_orders', 'far fa-circle', 7, 'Y', 1);

-- Dumping structure for table toko_elektronik.tbl_user
CREATE TABLE IF NOT EXISTS `tbl_user` (
  `id_user` int NOT NULL AUTO_INCREMENT,
  `username` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `full_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `id_level` int DEFAULT NULL,
  `image` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `nohp` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `is_active` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Y',
  PRIMARY KEY (`id_user`),
  KEY `id_level` (`id_level`),
  CONSTRAINT `tbl_user_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.tbl_user: ~0 rows (approximately)
INSERT INTO `tbl_user` (`id_user`, `username`, `full_name`, `password`, `id_level`, `image`, `nohp`, `email`, `is_active`) VALUES
	(1, 'admin', 'Administrator', '$2y$05$Bl1UXpDrO8843SqKlnGkq.AjnPhDIGAbfKAoVUkqpUAp4um3LtrbW', 1, 'admin.jpg', '08129837323', '<EMAIL>', 'Y');

-- Dumping structure for table toko_elektronik.tbl_userlevel
CREATE TABLE IF NOT EXISTS `tbl_userlevel` (
  `id_level` int NOT NULL AUTO_INCREMENT,
  `nama_level` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id_level`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

-- Dumping data for table toko_elektronik.tbl_userlevel: ~1 rows (approximately)
INSERT INTO `tbl_userlevel` (`id_level`, `nama_level`) VALUES
	(1, 'Administrator');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
