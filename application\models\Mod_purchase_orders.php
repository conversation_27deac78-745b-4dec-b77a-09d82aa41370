<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Mod_purchase_orders extends CI_Model
{
    var $table = 'purchase_orders';
    var $column_search = array('po_no', 'order_date', 's.nama', 'status', 'priority', 'total_amount');
    var $column_order = array('po_no', 'order_date', 's.nama', 'status', 'priority', 'total_amount');
    var $order = array('id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('po.*, s.nama as supplier_name');
        $this->db->from($this->table . ' po');
        $this->db->join('supplier s', 's.id = po.supplier_id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    function insert($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    function get($id)
    {
        $this->db->select('po.*, s.nama as supplier_name, s.alamat as supplier_address, s.no_telepon as supplier_phone');
        $this->db->from($this->table . ' po');
        $this->db->join('supplier s', 's.id = po.supplier_id', 'left');
        $this->db->where('po.id', $id);
        return $this->db->get()->row();
    }

    function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
    }

    function get_last_po_number($year, $month)
    {
        $this->db->select('po_no');
        $this->db->from($this->table);
        $this->db->like('po_no', 'PO-' . $year . $month, 'after');
        $this->db->order_by('po_no', 'desc');
        $this->db->limit(1);
        return $this->db->get()->row();
    }

    // Methods for purchase order detail
    var $table_detail = 'purchase_order_items';
    var $column_search_detail = array('b.nama', 's.nama', 'poi.quantity', 'poi.unit_price', 'poi.notes');
    var $column_order_detail = array('b.nama', 's.nama', 'poi.quantity', 'poi.unit_price', 'poi.notes');
    var $order_detail = array('poi.id' => 'desc');

    private function _get_datatables_query_detail($purchase_order_id)
    {
        $this->db->select('poi.*, b.nama, s.nama as satuan_nama');
        $this->db->from($this->table_detail . ' poi');
        $this->db->join('barang b', 'b.id = poi.id_barang', 'left');
        $this->db->join('satuan s', 's.id = b.id_satuan', 'left');
        $this->db->where('poi.purchase_order_id', $purchase_order_id);

        $i = 0;
        foreach ($this->column_search_detail as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search_detail) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order_detail[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order_detail)) {
            $order = $this->order_detail;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables_detail($purchase_order_id)
    {
        $this->_get_datatables_query_detail($purchase_order_id);
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_detail($purchase_order_id)
    {
        $this->_get_datatables_query_detail($purchase_order_id);
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all_detail($purchase_order_id)
    {
        $this->db->from($this->table_detail);
        $this->db->where('purchase_order_id', $purchase_order_id);
        return $this->db->count_all_results();
    }

    function insert_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    function get_detail($id)
    {
        $this->db->select('poi.*, b.nama as barang_nama');
        $this->db->from($this->table_detail . ' poi');
        $this->db->join('barang b', 'b.id = poi.id_barang', 'left');
        $this->db->where('poi.id', $id);
        return $this->db->get()->row();
    }

    function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    function get_purchase_order_summary($purchase_order_id)
    {
        $this->db->select('COUNT(*) as total_items, SUM(quantity) as total_qty, SUM(quantity * unit_price - discount) as grand_total');
        $this->db->from($this->table_detail);
        $this->db->where('purchase_order_id', $purchase_order_id);
        $result = $this->db->get()->row();

        return array(
            'total_items' => $result->total_items ? $result->total_items : 0,
            'total_qty' => $result->total_qty ? $result->total_qty : 0,
            'grand_total' => $result->grand_total ? $result->grand_total : 0
        );
    }
}
